"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, DialogTitle } from "@/components/ui/dialog"
import { useAuthModal } from "@/context/auth-modal-context"
import { LoginForm } from "@/components/login-form"
import { SignUpForm } from "@/components/signup-form"
import { Button } from "@/components/ui/button"

export function AuthModal() {
  const { isOpen, view, closeModal, setView } = useAuthModal()

  return (
    <Dialog open={isOpen} onOpenChange={closeModal}>
      <DialogContent className="sm:max-w-[425px]">
        <DialogHeader>
          <DialogTitle className="text-center">
            {view === "login" ? "Login to your account" : "Create an account"}
          </DialogTitle>
        </DialogHeader>
        <div className="py-4">{view === "login" ? <LoginForm /> : <SignUpForm />}</div>
        <div className="text-center text-sm text-muted-foreground">
          {view === "login" ? (
            <>
              Don't have an account?{" "}
              <Button variant="link" onClick={() => setView("signup")} className="p-0 h-auto">
                Sign Up
              </Button>
            </>
          ) : (
            <>
              Already have an account?{" "}
              <Button variant="link" onClick={() => setView("login")} className="p-0 h-auto">
                Login
              </Button>
            </>
          )}
        </div>
      </DialogContent>
    </Dialog>
  )
}

// Placeholder for LoginForm and SignUpForm
// In a real application, these would be separate components with their own logic.

// LoginForm component is already defined in a separate file, so no need to redefine here

// SignUpForm component is already defined in a separate file, so no need to redefine here
