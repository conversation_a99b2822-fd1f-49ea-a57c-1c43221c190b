import type React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTit<PERSON> } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { History, MapPin, DollarSign } from "lucide-react"
import Link from "next/link"
import { cn } from "@/lib/utils"

interface SearchItem {
  id: string
  query: string
  location?: string
  priceRange?: string
  date: string
}

interface RecentSearchesProps extends React.HTMLAttributes<HTMLDivElement> {}

async function getRecentSearches(): Promise<SearchItem[]> {
  // Simulate fetching recent searches from a user's history
  return [
    {
      id: "s1",
      query: "3 bedroom apartment",
      location: "Victoria Island, Lagos",
      priceRange: "₦2M - ₦5M",
      date: "2 days ago",
    },
    {
      id: "s2",
      query: "land for sale",
      location: "Lekki, Lagos",
      priceRange: "₦50M+",
      date: "1 week ago",
    },
    {
      id: "s3",
      query: "duplex for rent",
      location: "Abuja",
      priceRange: "₦3M - ₦8M",
      date: "3 weeks ago",
    },
  ]
}

export async function RecentSearches({ className, ...props }: RecentSearchesProps) {
  const searches = await getRecentSearches()

  return (
    <Card className={cn("shadow-lg", className)} {...props}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <History className="h-6 w-6" /> Recent Searches
        </CardTitle>
      </CardHeader>
      <CardContent>
        {searches.length === 0 ? (
          <p className="text-muted-foreground">No recent searches found.</p>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
            {searches.map((search) => (
              <Card key={search.id} className="p-4 hover:bg-muted/50 transition-colors">
                <Link
                  href={`/search?query=${encodeURIComponent(search.query)}&location=${encodeURIComponent(search.location || "")}`}
                  className="block"
                >
                  <h4 className="font-semibold text-lg mb-1">{search.query}</h4>
                  <p className="text-sm text-muted-foreground flex items-center gap-1">
                    <MapPin className="h-4 w-4" /> {search.location}
                  </p>
                  {search.priceRange && (
                    <p className="text-sm text-muted-foreground flex items-center gap-1">
                      <DollarSign className="h-4 w-4" /> {search.priceRange}
                    </p>
                  )}
                  <p className="text-xs text-muted-foreground mt-2">Searched {search.date}</p>
                </Link>
              </Card>
            ))}
          </div>
        )}
        <div className="mt-6 text-center">
          <Button variant="outline" asChild>
            <Link href="/search">View All Searches</Link>
          </Button>
        </div>
      </CardContent>
    </Card>
  )
}
