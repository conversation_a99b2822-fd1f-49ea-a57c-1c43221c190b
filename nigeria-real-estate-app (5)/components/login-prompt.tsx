"use client"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { useAuthModal } from "@/context/auth-modal-context"
import { LogIn } from "lucide-react"

export function LoginPrompt() {
  const { openModal } = useAuthModal()

  return (
    <Card className="w-full max-w-md mx-auto text-center">
      <CardHeader>
        <CardTitle className="flex items-center justify-center gap-2">
          <LogIn className="h-6 w-6" /> Login Required
        </CardTitle>
        <CardDescription>Please login or sign up to access this feature.</CardDescription>
      </CardHeader>
      <CardContent>
        <Button onClick={() => openModal("login")} className="w-full">
          Login / Sign Up
        </Button>
      </CardContent>
    </Card>
  )
}
