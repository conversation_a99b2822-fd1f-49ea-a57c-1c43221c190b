"use client"

import { useState } from "react"
import { <PERSON>, <PERSON><PERSON><PERSON>nt, Card<PERSON><PERSON>er, Card<PERSON>itle } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { Plus, XCircle, CheckCircle } from "lucide-react"
import Image from "next/image"

interface Property {
  id: string
  title: string
  location: string
  price: string
  bedrooms: number
  bathrooms: number
  size: number
  features: string[]
  imageUrl: string
}

interface PropertyComparisonProps {
  currentPropertyId: string
}

const allAvailableProperties: Property[] = [
  {
    id: "prop_123",
    title: "Luxurious 5-Bedroom Villa",
    location: "Banana Island, Ikoyi, Lagos",
    price: "₦450,000,000",
    bedrooms: 5,
    bathrooms: 6,
    size: 800,
    features: ["Private Pool", "Garden", "24/7 Security", "Smart Home System", "Generator", "Parking"],
    imageUrl: "/placeholder.svg?height=150&width=200",
  },
  {
    id: "prop_124",
    title: "Modern 4-Bedroom Duplex",
    location: "Lekki Phase 1, Lagos",
    price: "₦120,000,000",
    bedrooms: 4,
    bathrooms: 4,
    size: 450,
    features: ["Gated Community", "Gym Access", "Serviced", "Parking"],
    imageUrl: "/placeholder.svg?height=150&width=200",
  },
  {
    id: "prop_125",
    title: "Spacious 3-Bedroom Apartment",
    location: "Victoria Island, Lagos",
    price: "₦85,000,000",
    bedrooms: 3,
    bathrooms: 3,
    size: 200,
    features: ["Ocean View", "Elevator", "24/7 Power", "Parking"],
    imageUrl: "/placeholder.svg?height=150&width=200",
  },
  {
    id: "prop_126",
    title: "Cozy 2-Bedroom Bungalow",
    location: "Yaba, Lagos",
    price: "₦30,000,000",
    bedrooms: 2,
    bathrooms: 2,
    size: 150,
    features: ["Quiet Neighborhood", "Garden", "Fenced"],
    imageUrl: "/placeholder.svg?height=150&width=200",
  },
]

export function PropertyComparison({ currentPropertyId }: PropertyComparisonProps) {
  const currentProperty = allAvailableProperties.find((p) => p.id === currentPropertyId)
  const [comparedProperties, setComparedProperties] = useState<Property[]>([])
  const [selectedPropertyId, setSelectedPropertyId] = useState<string>("")

  const handleAddProperty = () => {
    const propertyToAdd = allAvailableProperties.find((p) => p.id === selectedPropertyId)
    if (
      propertyToAdd &&
      !comparedProperties.some((p) => p.id === propertyToAdd.id) &&
      propertyToAdd.id !== currentPropertyId
    ) {
      setComparedProperties([...comparedProperties, propertyToAdd])
      setSelectedPropertyId("") // Reset select
    }
  }

  const handleRemoveProperty = (id: string) => {
    setComparedProperties(comparedProperties.filter((p) => p.id !== id))
  }

  if (!currentProperty) {
    return null // Or handle error/loading state
  }

  const allPropertiesToCompare = [currentProperty, ...comparedProperties]
  const allFeatures = Array.from(new Set(allPropertiesToCompare.flatMap((p) => p.features)))

  return (
    <Card className="shadow-lg">
      <CardHeader>
        <CardTitle>Compare Properties</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="flex flex-wrap items-center gap-4 mb-6">
          <Select value={selectedPropertyId} onValueChange={setSelectedPropertyId}>
            <SelectTrigger className="w-[250px]">
              <SelectValue placeholder="Select property to compare" />
            </SelectTrigger>
            <SelectContent>
              {allAvailableProperties
                .filter((p) => p.id !== currentPropertyId && !comparedProperties.some((cp) => cp.id === p.id))
                .map((p) => (
                  <SelectItem key={p.id} value={p.id}>
                    {p.title} ({p.location})
                  </SelectItem>
                ))}
            </SelectContent>
          </Select>
          <Button onClick={handleAddProperty} disabled={!selectedPropertyId || comparedProperties.length >= 2}>
            <Plus className="h-4 w-4 mr-2" /> Add for Comparison
          </Button>
        </div>

        {allPropertiesToCompare.length <= 1 ? (
          <p className="text-muted-foreground">Add up to 2 more properties to compare side-by-side.</p>
        ) : (
          <div className="overflow-x-auto">
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead className="w-[150px]">Feature</TableHead>
                  {allPropertiesToCompare.map((p) => (
                    <TableHead key={p.id} className="text-center relative">
                      <Image
                        src={p.imageUrl || "/placeholder.svg"}
                        alt={p.title}
                        width={100}
                        height={75}
                        className="mx-auto mb-2 rounded-md object-cover"
                      />
                      <h4 className="font-semibold text-base">{p.title}</h4>
                      <p className="text-xs text-muted-foreground">{p.location}</p>
                      {p.id !== currentPropertyId && (
                        <Button
                          variant="ghost"
                          size="icon"
                          className="absolute top-0 right-0 h-6 w-6 text-muted-foreground hover:text-destructive"
                          onClick={() => handleRemoveProperty(p.id)}
                        >
                          <XCircle className="h-4 w-4" />
                          <span className="sr-only">Remove {p.title}</span>
                        </Button>
                      )}
                    </TableHead>
                  ))}
                </TableRow>
              </TableHeader>
              <TableBody>
                <TableRow>
                  <TableCell className="font-medium">Price</TableCell>
                  {allPropertiesToCompare.map((p) => (
                    <TableCell key={p.id} className="text-center font-bold text-primary">
                      {p.price}
                    </TableCell>
                  ))}
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Bedrooms</TableCell>
                  {allPropertiesToCompare.map((p) => (
                    <TableCell key={p.id} className="text-center">
                      {p.bedrooms}
                    </TableCell>
                  ))}
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Bathrooms</TableCell>
                  {allPropertiesToCompare.map((p) => (
                    <TableCell key={p.id} className="text-center">
                      {p.bathrooms}
                    </TableCell>
                  ))}
                </TableRow>
                <TableRow>
                  <TableCell className="font-medium">Size (sqm)</TableCell>
                  {allPropertiesToCompare.map((p) => (
                    <TableCell key={p.id} className="text-center">
                      {p.size}
                    </TableCell>
                  ))}
                </TableRow>
                {allFeatures.map((feature) => (
                  <TableRow key={feature}>
                    <TableCell className="font-medium">{feature}</TableCell>
                    {allPropertiesToCompare.map((p) => (
                      <TableCell key={`${p.id}-${feature}`} className="text-center">
                        {p.features.includes(feature) ? (
                          <CheckCircle className="h-5 w-5 text-green-500 mx-auto" />
                        ) : (
                          <XCircle className="h-5 w-5 text-muted-foreground mx-auto" />
                        )}
                      </TableCell>
                    ))}
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
