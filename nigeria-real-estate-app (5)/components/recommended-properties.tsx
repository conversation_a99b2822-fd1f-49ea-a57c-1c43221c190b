import type React from "react"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { PropertyCard } from "@/components/property-card"
import { Lightbulb } from "lucide-react"
import { cn } from "@/lib/utils"

interface Property {
  id: string
  title: string
  location: string
  price: string
  priceType: string
  purpose: "rent" | "sale"
  type: string
  bedrooms: number
  bathrooms: number
  imageUrl: string
  coordinates: { lat: number; lng: number }
}

interface RecommendedPropertiesProps extends React.HTMLAttributes<HTMLDivElement> {
  currentPropertyId?: string // Optional: to exclude the current property from recommendations
}

async function getRecommendedProperties(excludeId?: string): Promise<Property[]> {
  // Simulate fetching recommended properties.
  // In a real application, this would involve a recommendation engine.
  const allProperties: Property[] = [
    {
      id: "rec_1",
      title: "Spacious 4-Bedroom Duplex",
      location: "Lekki Phase 1, Lagos",
      price: "₦3,500,000",
      priceType: "year",
      purpose: "rent",
      type: "Duplex",
      bedrooms: 4,
      bathrooms: 4,
      imageUrl: "/placeholder.svg?height=200&width=300",
      coordinates: { lat: 6.4531, lng: 3.4 },
    },
    {
      id: "rec_2",
      title: "Modern 2-Bedroom Apartment",
      location: "Ikoyi, Lagos",
      price: "₦1,800,000",
      priceType: "year",
      purpose: "rent",
      type: "Apartment",
      bedrooms: 2,
      bathrooms: 2,
      imageUrl: "/placeholder.svg?height=200&width=300",
      coordinates: { lat: 6.4281, lng: 3.4219 },
    },
    {
      id: "rec_3",
      title: "Luxury 6-Bedroom Mansion",
      location: "Asokoro, Abuja",
      price: "₦750,000,000",
      priceType: "sale",
      purpose: "sale",
      type: "Mansion",
      bedrooms: 6,
      bathrooms: 7,
      imageUrl: "/placeholder.svg?height=200&width=300",
      coordinates: { lat: 9.0765, lng: 7.3986 },
    },
    {
      id: "rec_4",
      title: "Commercial Office Space",
      location: "Central Business District, Abuja",
      price: "₦15,000,000",
      priceType: "year",
      purpose: "rent",
      type: "Office",
      bedrooms: 0,
      bathrooms: 2,
      imageUrl: "/placeholder.svg?height=200&width=300",
      coordinates: { lat: 9.0579, lng: 7.4951 },
    },
  ]

  return allProperties.filter((p) => p.id !== excludeId)
}

export async function RecommendedProperties({ currentPropertyId, className, ...props }: RecommendedPropertiesProps) {
  const recommended = await getRecommendedProperties(currentPropertyId)

  if (recommended.length === 0) {
    return null
  }

  return (
    <Card className={cn("shadow-lg", className)} {...props}>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Lightbulb className="h-6 w-6" /> Recommended for You
        </CardTitle>
      </CardHeader>
      <CardContent>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
          {recommended.map((property) => (
            <PropertyCard key={property.id} property={property} />
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
