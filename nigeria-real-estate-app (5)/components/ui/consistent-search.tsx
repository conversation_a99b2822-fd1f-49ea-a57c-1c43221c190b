"use client"

import * as React from "react"
import { cn } from "@/lib/utils"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, SlidersHorizontal } from "lucide-react"
import type { ReactNode } from "react"

interface FilterOption {
  value: string
  label: string
  count?: number
}

interface ConsistentSearchProps {
  placeholder?: string
  searchValue?: string
  onSearchChange?: (value: string) => void
  filters?: Array<{
    placeholder: string
    value: string
    onChange: (value: string) => void
    options: FilterOption[]
  }>
  onAdvancedFilters?: () => void
  quickFilters?: ReactNode
  className?: string
  onSearch?: (query: string) => void
}

const ConsistentSearch = React.forwardRef<HTMLInputElement, React.InputHTMLAttributes<HTMLInputElement>>(
  (
    {
      className,
      placeholder = "Search...",
      searchValue = "",
      onSearchChange,
      filters = [],
      onAdvancedFilters,
      quickFilters,
      onSearch,
      ...props
    },
    ref,
  ) => (
    <div className={`bg-white px-4 py-4 border-b space-y-3 ${className}`}>
      {/* Main Search Bar */}
      <div className="relative ml-auto flex-1 md:grow-0">
        <Search className="absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground" />
        <Input
          ref={ref}
          type="search"
          placeholder={placeholder}
          className={cn("w-full rounded-lg bg-background pl-8 md:w-[200px] lg:w-[336px]", className)}
          value={searchValue}
          onChange={(e) => {
            onSearchChange?.(e.target.value)
            onSearch?.(e.target.value)
          }}
          {...props}
        />
      </div>

      {/* Filters Row */}
      {(filters.length > 0 || onAdvancedFilters) && (
        <div className="flex items-center gap-2 overflow-x-auto pb-2">
          {onAdvancedFilters && (
            <Button
              variant="outline"
              size="sm"
              className="whitespace-nowrap flex-shrink-0 hover:bg-gray-50 transition-colors bg-transparent"
              onClick={onAdvancedFilters}
            >
              <SlidersHorizontal className="h-4 w-4 mr-2" />
              Filters
            </Button>
          )}

          {filters.map((filter, index) => (
            <Select key={index} value={filter.value} onValueChange={filter.onChange}>
              <SelectTrigger className="w-32 h-9">
                <SelectValue placeholder={filter.placeholder} />
              </SelectTrigger>
              <SelectContent>
                {filter.options.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    <div className="flex items-center justify-between w-full">
                      <span>{option.label}</span>
                      {option.count && <span className="text-xs text-gray-500 ml-2">({option.count})</span>}
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          ))}
        </div>
      )}

      {/* Quick Filters */}
      {quickFilters && <div className="flex gap-2 overflow-x-auto pb-2">{quickFilters}</div>}
    </div>
  ),
)
ConsistentSearch.displayName = "ConsistentSearch"

export { ConsistentSearch }
