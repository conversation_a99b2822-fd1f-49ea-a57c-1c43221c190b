"use client"

import React from "react"

import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft, MessageCircle, PanelLeft } from "lucide-react"
import Link from "next/link"
import { useSidebar } from "@/components/ui/sidebar" // Assuming sidebar component exists
import { cn } from "@/lib/utils"
import type { ReactNode } from "react"

interface ConsistentHeaderProps {
  title: string
  subtitle?: string
  backHref?: string
  actions?: ReactNode
  showMessages?: boolean
  messageCount?: number
  className?: string
  showSidebarTrigger?: boolean
}

const ConsistentHeader = React.forwardRef<HTMLDivElement, ConsistentHeaderProps & React.HTMLAttributes<HTMLDivElement>>(
  (
    {
      title,
      subtitle,
      backHref,
      actions,
      showMessages = true,
      messageCount = 0,
      className = "",
      showSidebarTrigger = true,
      ...props
    },
    ref,
  ) => {
    const { toggleSidebar } = useSidebar()

    return (
      <header
        ref={ref}
        className={cn(
          "sticky top-0 z-30 flex h-14 items-center gap-4 border-b bg-background px-4 sm:static sm:h-auto sm:border-0 sm:bg-transparent sm:px-6",
          className,
        )}
        {...props}
      >
        {showSidebarTrigger && (
          <Button
            size="icon"
            variant="outline"
            className="sm:hidden bg-transparent"
            onClick={toggleSidebar} // Use the toggleSidebar from useSidebar hook
          >
            <PanelLeft className="h-5 w-5" />
            <span className="sr-only">Toggle Menu</span>
          </Button>
        )}
        {backHref && (
          <Link href={backHref}>
            <Button variant="ghost" size="sm" className="hover:bg-gray-100 transition-colors">
              <ArrowLeft className="h-4 w-4" />
            </Button>
          </Link>
        )}

        <div className="flex-1">
          <h1 className="text-lg font-semibold text-gray-900">{title}</h1>
          {subtitle && <p className="text-sm text-gray-600">{subtitle}</p>}
        </div>

        <div className="flex items-center gap-2">
          {showMessages && (
            <Button variant="ghost" size="sm" className="relative hover:bg-gray-100 transition-colors">
              <MessageCircle className="h-4 w-4" />
              {messageCount > 0 && (
                <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-4 w-4 flex items-center justify-center">
                  {messageCount}
                </span>
              )}
            </Button>
          )}
          {actions}
        </div>
      </header>
    )
  },
)

ConsistentHeader.displayName = "ConsistentHeader"

export { ConsistentHeader }
