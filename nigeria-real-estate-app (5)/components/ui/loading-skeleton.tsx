import type React from "react"
import { Skeleton } from "@/components/ui/skeleton"
import { cn } from "@/lib/utils"

interface LoadingSkeletonProps extends React.HTMLAttributes<HTMLDivElement> {
  count?: number
  itemClassName?: string
}

export function LoadingSkeleton({ count = 1, itemClassName, className, ...props }: LoadingSkeletonProps) {
  return (
    <div className={cn("space-y-4", className)} {...props}>
      {Array.from({ length: count }).map((_, i) => (
        <Skeleton key={i} className={cn("h-10 w-full", itemClassName)} />
      ))}
    </div>
  )
}
