import { <PERSON>, CardContent } from "@/components/ui/card"
import { But<PERSON> } from "@/components/ui/button"
import Image from "next/image"
import Link from "next/link"
import { MapPin, Bed, Bath, Heart } from "lucide-react"

interface PropertyCardProps {
  property: {
    id: string
    title: string
    location: string
    price: string
    priceType: string
    purpose: "rent" | "sale"
    type: string
    bedrooms: number
    bathrooms: number
    imageUrl: string
  }
}

export function PropertyCard({ property }: PropertyCardProps) {
  return (
    <Card className="flex flex-col overflow-hidden shadow-lg hover:shadow-xl transition-shadow duration-300">
      <Link href={`/property/${property.id}`} className="relative block h-48 w-full">
        <Image
          src={property.imageUrl || "/placeholder.svg"}
          alt={property.title}
          fill
          style={{ objectFit: "cover" }}
          className="rounded-t-lg"
        />
        <div className="absolute top-2 right-2 bg-background/80 rounded-full p-2">
          <Heart className="h-5 w-5 text-muted-foreground hover:text-red-500 transition-colors" />
        </div>
      </Link>
      <CardContent className="p-4 flex-1 flex flex-col">
        <h3 className="text-xl font-semibold mb-2 line-clamp-2">{property.title}</h3>
        <p className="text-sm text-muted-foreground flex items-center mb-2">
          <MapPin className="h-4 w-4 mr-1" /> {property.location}
        </p>
        <div className="flex items-center gap-4 text-muted-foreground text-sm mb-3">
          {property.bedrooms > 0 && (
            <div className="flex items-center">
              <Bed className="h-4 w-4 mr-1" /> {property.bedrooms} Beds
            </div>
          )}
          {property.bathrooms > 0 && (
            <div className="flex items-center">
              <Bath className="h-4 w-4 mr-1" /> {property.bathrooms} Baths
            </div>
          )}
        </div>
        <p className="text-2xl font-bold text-primary mb-4">
          {property.price}
          {property.purpose === "rent" && property.priceType && (
            <span className="text-base text-muted-foreground">/{property.priceType}</span>
          )}
        </p>
        <Button asChild className="w-full mt-auto">
          <Link href={`/property/${property.id}`}>View Details</Link>
        </Button>
      </CardContent>
    </Card>
  )
}
