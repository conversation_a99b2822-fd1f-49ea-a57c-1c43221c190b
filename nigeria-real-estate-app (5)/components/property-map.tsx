"use client"

import { useEffect, useRef } from "react"

interface Property {
  id: string
  title: string
  location: string
  coordinates: { lat: number; lng: number }
}

interface PropertyMapProps {
  properties?: Property[]
  lat?: number
  lng?: number
  zoom?: number
  className?: string
}

export function PropertyMap({ properties = [], lat, lng, zoom = 12, className }: PropertyMapProps) {
  const mapRef = useRef<HTMLDivElement>(null)
  const mapInstance = useRef<any | null>(null)
  const markersRef = useRef<any[]>([])

  useEffect(() => {
    if (typeof window === "undefined" || !mapRef.current) return

    const loadGoogleMapsScript = () => {
      if (window.google && window.google.maps) {
        initMap()
        return
      }

      const script = document.createElement("script")
      script.src = `https://maps.googleapis.com/maps/api/js?key=${process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY}&libraries=places`
      script.async = true
      script.defer = true
      script.onload = initMap
      document.head.appendChild(script)
    }

    const initMap = () => {
      if (!mapRef.current || !window.google || !window.google.maps) return

      const center =
        properties.length > 0 ? properties[0].coordinates : lat && lng ? { lat, lng } : { lat: 6.5244, lng: 3.3792 } // Default to Lagos, Nigeria

      mapInstance.current = new window.google.maps.Map(mapRef.current, {
        center: center,
        zoom: zoom,
        mapId: "DEMO_MAP_ID", // Replace with your actual Map ID for custom styling
      })

      updateMarkers()
    }

    const updateMarkers = () => {
      if (!mapInstance.current) return

      // Clear existing markers
      markersRef.current.forEach((marker) => marker.setMap(null))
      markersRef.current = []

      const locations =
        properties.length > 0
          ? properties
          : lat && lng
            ? [{ id: "single", title: "Property", location: "Location", coordinates: { lat, lng } }]
            : []

      locations.forEach((property) => {
        const marker = new window.google.maps.Marker({
          position: property.coordinates,
          map: mapInstance.current,
          title: property.title,
          icon: {
            path: "M12 2C8.13 2 5 5.13 5 9c0 5.25 7 13 7 13s7-7.75 7-13c0-3.87-3.13-7-7-7zm0 9.5c-1.38 0-2.5-1.12-2.5-2.5S10.62 6.5 12 6.5s2.5 1.12 2.5 2.5S13.38 11.5 12 11.5z",
            fillColor: "#FF0000", // Red color for marker
            fillOpacity: 0.9,
            scale: 2,
            strokeWeight: 0,
          },
        })

        const infoWindow = new window.google.maps.InfoWindow({
          content: `
            <div style="font-family: sans-serif; padding: 5px;">
              <h4 style="margin: 0 0 5px 0; font-size: 16px;">${property.title}</h4>
              <p style="margin: 0; font-size: 13px; color: #555;">${property.location}</p>
              <a href="/property/${property.id}" style="color: #007bff; text-decoration: none; font-size: 13px;">View Details</a>
            </div>
          `,
        })

        marker.addListener("click", () => {
          infoWindow.open(mapInstance.current, marker)
        })
        markersRef.current.push(marker)
      })

      if (locations.length > 0 && mapInstance.current) {
        const bounds = new window.google.maps.LatLngBounds()
        locations.forEach((property) => bounds.extend(property.coordinates))
        mapInstance.current.fitBounds(bounds)
      }
    }

    loadGoogleMapsScript()

    // Cleanup function
    return () => {
      if (markersRef.current) {
        markersRef.current.forEach((marker) => marker.setMap(null))
      }
      mapInstance.current = null
    }
  }, [properties, lat, lng, zoom])

  return (
    <div ref={mapRef} className={className} aria-label="Google Map showing property locations">
      {!process.env.NEXT_PUBLIC_GOOGLE_MAPS_API_KEY && (
        <div className="flex items-center justify-center h-full bg-muted text-muted-foreground rounded-lg">
          <p>Google Maps API Key is missing. Please set NEXT_PUBLIC_GOOGLE_MAPS_API_KEY.</p>
        </div>
      )}
    </div>
  )
}
