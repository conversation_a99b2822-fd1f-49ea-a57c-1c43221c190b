import NextAuth from "next-auth"
import Cred<PERSON><PERSON><PERSON><PERSON>ider from "next-auth/providers/credentials"

export const {
  handlers: { GET, POST },
  auth,
  signIn,
  signOut,
} = NextAuth({
  providers: [
    CredentialsProvider({
      name: "Credentials",
      credentials: {
        email: { label: "Email", type: "text" },
        password: { label: "Password", type: "password" },
      },
      async authorize(credentials) {
        // This is where you would query your database to find the user
        // For demonstration, we'll use hardcoded users
        const users = [
          { id: "1", email: "<EMAIL>", password: "password", name: "Tenant User", role: "tenant" },
          { id: "2", email: "<EMAIL>", password: "password", name: "Landlord User", role: "landlord" },
          { id: "3", email: "<EMAIL>", password: "password", name: "Manager User", role: "manager" },
          { id: "4", email: "<EMAIL>", password: "password", name: "Admin User", role: "admin" },
        ]

        const user = users.find((u) => u.email === credentials?.email && u.password === credentials?.password)

        if (user) {
          // Any object returned will be saved in `user` property of the JWT
          return { id: user.id, name: user.name, email: user.email, role: user.role }
        } else {
          // If you return null then an error will be displayed advising the user to check their details.
          throw new Error("Invalid credentials")
        }
      },
    }),
  ],
  callbacks: {
    async jwt({ token, user }) {
      if (user) {
        token.role = user.role
      }
      return token
    },
    async session({ session, token }) {
      if (session.user) {
        session.user.role = token.role as string
      }
      return session
    },
  },
  pages: {
    signIn: "/", // Redirect to home page for sign in
    error: "/", // Redirect to home page for errors
  },
  secret: process.env.NEXTAUTH_SECRET,
})
