// lib/search-algorithm.ts

// This file would contain the core logic for your advanced search algorithm.
// For demonstration, we'll provide a simplified structure based on the documentation.

interface Property {
  id: string
  title: string
  description: string
  location: string
  price: number
  propertyType: string
  purpose: "rent" | "sale"
  bedrooms?: number
  bathrooms?: number
  size?: number // in sqm
  features: string[]
  amenities: string[]
  verified: boolean
  createdAt: Date
  views: number
  saves: number
  contacts: number
  coordinates: { lat: number; lng: number }
}

interface SearchCriteria {
  query?: string
  location?: string
  propertyType?: string
  purpose?: "rent" | "sale"
  minPrice?: number
  maxPrice?: number
  bedrooms?: number
  bathrooms?: number
  features?: string[]
  verified?: boolean
  userId?: string // For personalization
}

interface SearchResult extends Property {
  relevanceScore: number
}

// --- Mock Data (for demonstration) ---
const mockProperties: Property[] = [
  {
    id: "p1",
    title: "Modern 3-Bed Apartment",
    description: "Spacious apartment with great views.",
    location: "Victoria Island, Lagos",
    price: 2500000,
    propertyType: "apartment",
    purpose: "rent",
    bedrooms: 3,
    bathrooms: 2,
    size: 150,
    features: ["Parking", "Security"],
    amenities: ["Gym"],
    verified: true,
    createdAt: new Date("2024-07-01"),
    views: 120,
    saves: 10,
    contacts: 5,
    coordinates: { lat: 6.4281, lng: 3.4219 },
  },
  {
    id: "p2",
    title: "Luxury 5-Bed Duplex",
    description: "Exquisite duplex in a serene environment.",
    location: "Ikoyi, Lagos",
    price: 180000000,
    propertyType: "duplex",
    purpose: "sale",
    bedrooms: 5,
    bathrooms: 6,
    size: 400,
    features: ["Private Pool", "Garden"],
    amenities: ["24/7 Power"],
    verified: true,
    createdAt: new Date("2024-06-15"),
    views: 300,
    saves: 25,
    contacts: 15,
    coordinates: { lat: 6.4531, lng: 3.4 },
  },
  {
    id: "p3",
    title: "Commercial Land",
    description: "Prime land suitable for commercial development.",
    location: "Lekki, Lagos",
    price: 90000000,
    propertyType: "land",
    purpose: "sale",
    bedrooms: 0,
    bathrooms: 0,
    size: 1000,
    features: [],
    amenities: [],
    verified: false,
    createdAt: new Date("2024-05-20"),
    views: 80,
    saves: 5,
    contacts: 2,
    coordinates: { lat: 6.4654, lng: 3.5875 },
  },
  {
    id: "p4",
    title: "Cozy 2-Bed Flat",
    description: "Affordable flat near university.",
    location: "Yaba, Lagos",
    price: 1200000,
    propertyType: "apartment",
    purpose: "rent",
    bedrooms: 2,
    bathrooms: 1,
    size: 80,
    features: ["Fenced"],
    amenities: [],
    verified: true,
    createdAt: new Date("2024-07-10"),
    views: 90,
    saves: 8,
    contacts: 3,
    coordinates: { lat: 6.5175, lng: 3.3941 },
  },
  {
    id: "p5",
    title: "4-Bed Detached House",
    description: "Family home in a quiet neighborhood.",
    location: "Abuja, FCT",
    price: 75000000,
    propertyType: "house",
    purpose: "sale",
    bedrooms: 4,
    bathrooms: 4,
    size: 300,
    features: ["Garden", "Security"],
    amenities: ["Playground"],
    verified: true,
    createdAt: new Date("2024-06-01"),
    views: 200,
    saves: 18,
    contacts: 10,
    coordinates: { lat: 9.0765, lng: 7.3986 },
  },
]

// --- Scoring Functions (Simplified) ---

function calculateTextRelevance(property: Property, query?: string): number {
  if (!query) return 0
  const lowerQuery = query.toLowerCase()
  let score = 0
  if (property.title.toLowerCase().includes(lowerQuery)) score += 30
  if (property.description.toLowerCase().includes(lowerQuery)) score += 15
  if (property.location.toLowerCase().includes(lowerQuery)) score += 25
  property.features.forEach((f) => {
    if (f.toLowerCase().includes(lowerQuery)) score += 5
  })
  property.amenities.forEach((a) => {
    if (a.toLowerCase().includes(lowerQuery)) score += 5
  })
  return Math.min(100, score) // Cap at 100
}

function calculateLocationRelevance(property: Property, targetLocation?: string): number {
  if (!targetLocation) return 0
  // Simple string match for demonstration. In real app, use geo-spatial queries.
  return property.location.toLowerCase().includes(targetLocation.toLowerCase()) ? 100 : 0
}

function calculatePriceRelevance(property: Property, priceRange?: [number, number]): number {
  if (!priceRange) return 0
  const [min, max] = priceRange
  if (property.price >= min && property.price <= max) {
    return 100 // Perfect match
  }
  // Simple linear decay for prices outside range
  if (property.price < min) return Math.max(0, 100 - ((min - property.price) / min) * 50)
  if (property.price > max) return Math.max(0, 100 - ((property.price - max) / max) * 50)
  return 0
}

function calculateFeatureRelevance(property: Property, requiredFeatures?: string[]): number {
  if (!requiredFeatures || requiredFeatures.length === 0) return 0
  let matchedCount = 0
  requiredFeatures.forEach((reqFeature) => {
    if (property.features.some((f) => f.toLowerCase() === reqFeature.toLowerCase())) {
      matchedCount++
    }
  })
  return (matchedCount / requiredFeatures.length) * 100
}

function calculateQualityScore(property: Property): number {
  let score = 0
  if (property.verified) score += 30
  score += Math.min(20, property.views / 10) // Views contribute to quality
  score += Math.min(10, property.saves * 2) // Saves contribute more
  const daysOld = (new Date().getTime() - property.createdAt.getTime()) / (1000 * 60 * 60 * 24)
  score += Math.max(0, 40 - daysOld / 5) // Newer properties get a boost
  return Math.min(100, score)
}

// --- Main Search Function ---

export function searchProperties(criteria: SearchCriteria): SearchResult[] {
  const filteredProperties = mockProperties.filter((property) => {
    // Apply basic filters
    if (criteria.location && !property.location.toLowerCase().includes(criteria.location.toLowerCase())) return false
    if (criteria.purpose && property.purpose !== criteria.purpose) return false
    if (criteria.propertyType && property.propertyType.toLowerCase() !== criteria.propertyType.toLowerCase())
      return false
    if (criteria.minPrice && property.price < criteria.minPrice) return false
    if (criteria.maxPrice && property.price > criteria.maxPrice) return false
    if (criteria.bedrooms && property.bedrooms !== criteria.bedrooms) return false
    if (criteria.bathrooms && property.bathrooms !== criteria.bathrooms) return false
    if (criteria.verified && !property.verified) return false

    // Apply feature filters
    if (criteria.features && criteria.features.length > 0) {
      const hasAllFeatures = criteria.features.every((reqFeature) =>
        property.features.some((f) => f.toLowerCase() === reqFeature.toLowerCase()),
      )
      if (!hasAllFeatures) return false
    }

    return true
  })

  // Calculate relevance score for filtered properties
  const scoredResults: SearchResult[] = filteredProperties.map((property) => {
    let baseScore = 0
    baseScore += calculateTextRelevance(property, criteria.query) * 0.3
    baseScore += calculateLocationRelevance(property, criteria.location) * 0.25
    baseScore +=
      calculatePriceRelevance(
        property,
        criteria.minPrice && criteria.maxPrice ? [criteria.minPrice, criteria.maxPrice] : undefined,
      ) * 0.2
    baseScore += calculateFeatureRelevance(property, criteria.features) * 0.15
    baseScore += calculateQualityScore(property) * 0.1

    // Apply ML-based personalization (simplified)
    let personalizedScore = baseScore
    if (criteria.userId) {
      // In a real system, this would involve fetching user preferences
      // and applying collaborative/content-based filtering boosts.
      // For demo, let's just add a random boost for "personalized" feel.
      personalizedScore *= 1 + Math.random() * 0.1 // Up to 10% boost
    }

    return { ...property, relevanceScore: Math.round(personalizedScore) }
  })

  // Sort by relevance score (descending)
  scoredResults.sort((a, b) => b.relevanceScore - a.relevanceScore)

  return scoredResults
}

// --- Recommendation Function (Simplified) ---

export function getRecommendations(userId: string, count = 4): Property[] {
  // In a real system, this would use user interaction history,
  // collaborative filtering, and content-based filtering.
  // For demo, return a mix of popular and random properties not already in mockProperties.
  const popularProperties = mockProperties.sort((a, b) => b.views + b.saves * 2 - (a.views + a.saves * 2))

  const additionalProperties: Property[] = [
    {
      id: "rec_a",
      title: "Spacious 4-Bed Duplex",
      description: "Family-friendly duplex with modern finishes.",
      location: "Lekki Phase 1, Lagos",
      price: 3500000,
      propertyType: "duplex",
      purpose: "rent",
      bedrooms: 4,
      bathrooms: 4,
      size: 250,
      features: ["Gated Community", "Serviced"],
      amenities: ["Playground"],
      verified: true,
      createdAt: new Date("2024-07-18"),
      views: 80,
      saves: 7,
      contacts: 4,
      coordinates: { lat: 6.4531, lng: 3.4 },
    },
    {
      id: "rec_b",
      title: "Luxury 6-Bed Mansion",
      description: "Grand mansion with extensive grounds.",
      location: "Asokoro, Abuja",
      price: 750000000,
      propertyType: "mansion",
      purpose: "sale",
      bedrooms: 6,
      bathrooms: 7,
      size: 1200,
      features: ["Private Pool", "Cinema Room"],
      amenities: ["Smart Home"],
      verified: true,
      createdAt: new Date("2024-07-05"),
      views: 400,
      saves: 30,
      contacts: 20,
      coordinates: { lat: 9.0765, lng: 7.3986 },
    },
    {
      id: "rec_c",
      title: "Commercial Office Space",
      description: "Prime office space in CBD.",
      location: "Central Business District, Abuja",
      price: 15000000,
      propertyType: "office",
      purpose: "rent",
      bedrooms: 0,
      bathrooms: 2,
      size: 500,
      features: ["24/7 Power", "Elevator"],
      amenities: ["Parking"],
      verified: true,
      createdAt: new Date("2024-07-12"),
      views: 60,
      saves: 3,
      contacts: 1,
      coordinates: { lat: 9.0579, lng: 7.4951 },
    },
  ]

  const combined = [...popularProperties, ...additionalProperties]
  const uniqueIds = new Set<string>()
  const uniqueRecommendations: Property[] = []

  for (const prop of combined) {
    if (!uniqueIds.has(prop.id)) {
      uniqueIds.add(prop.id)
      uniqueRecommendations.push(prop)
    }
  }

  return uniqueRecommendations.slice(0, count)
}
