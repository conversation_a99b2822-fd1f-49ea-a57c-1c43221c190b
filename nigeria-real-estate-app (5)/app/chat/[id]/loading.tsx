import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

export default function ChatLoading() {
  return (
    <div className="flex flex-col h-full p-4">
      <Card className="flex-1 flex flex-col">
        <CardHeader className="flex flex-row items-center justify-between pb-3">
          <div className="flex items-center gap-3">
            <Skeleton className="h-5 w-5 rounded-full" /> {/* Back button skeleton */}
            <Skeleton className="h-10 w-10 rounded-full" />
            <div>
              <CardTitle className="text-lg">
                <Skeleton className="h-6 w-32" />
              </CardTitle>
              <p className="text-sm text-muted-foreground">
                <Skeleton className="h-4 w-20" />
              </p>
            </div>
          </div>
        </CardHeader>
        <CardContent className="flex-1 flex flex-col">
          <div className="flex-1 p-4 border rounded-md mb-4 space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className={`flex items-start gap-3 ${i % 2 === 0 ? "" : "justify-end"}`}>
                <Skeleton className="h-10 w-10 rounded-full" />
                <div className={`flex flex-col ${i % 2 === 0 ? "items-start" : "items-end"}`}>
                  <Skeleton className="h-10 w-48 rounded-lg" />
                  <Skeleton className="h-3 w-20 mt-1" />
                </div>
              </div>
            ))}
          </div>
          <div className="flex gap-2">
            <Skeleton className="flex-1 h-10" />
            <Skeleton className="h-10 w-10 rounded-md" />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
