import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Send, ChevronLeft } from "lucide-react"
import Link from "next/link"

interface ChatMessage {
  id: number
  sender: "You" | string
  avatar: string
  content: string
  time: string
}

interface ChatUser {
  id: string
  name: string
  avatar: string
  status: "online" | "offline"
}

async function getChatDetails(id: string): Promise<{ user: ChatUser; messages: ChatMessage[] }> {
  // Simulate fetching chat details and messages
  const chatUser: ChatUser = {
    id: "user-" + id,
    name: id === "agent-123" ? "Grace <PERSON> (Agent)" : "<PERSON> (Tenant)",
    avatar: id === "agent-123" ? "/placeholder.svg?height=40&width=40" : "/placeholder.svg?height=40&width=40",
    status: "online",
  }

  const chatMessages: ChatMessage[] = [
    {
      id: 1,
      sender: chatUser.name,
      avatar: chatUser.avatar,
      content: "Hello, I'm interested in the property at Lekki Phase 1. Is it still available?",
      time: "10:00 AM",
    },
    {
      id: 2,
      sender: "You",
      avatar: "/placeholder.svg?height=40&width=40",
      content: "Yes, it is! What would you like to know about it?",
      time: "10:05 AM",
    },
    {
      id: 3,
      sender: chatUser.name,
      avatar: chatUser.avatar,
      content: "Can you tell me about the average utility costs?",
      time: "10:10 AM",
    },
    {
      id: 4,
      sender: "You",
      avatar: "/placeholder.svg?height=40&width=40",
      content: "Sure, based on previous tenants, utilities average around ₦50,000 per month.",
      time: "10:15 AM",
    },
    {
      id: 5,
      sender: chatUser.name,
      avatar: chatUser.avatar,
      content: "Okay, thank you. I'd like to schedule a viewing.",
      time: "10:20 AM",
    },
  ]

  return { user: chatUser, messages: chatMessages }
}

export default async function ChatDetailPage({ params }: { params: { id: string } }) {
  const { user, messages } = await getChatDetails(params.id)

  return (
    <div className="flex flex-col h-full p-4">
      <Card className="flex-1 flex flex-col">
        <CardHeader className="flex flex-row items-center justify-between pb-3">
          <div className="flex items-center gap-3">
            <Link href="/messages" className="text-muted-foreground hover:text-primary">
              <ChevronLeft className="h-5 w-5" />
            </Link>
            <Avatar>
              <AvatarImage src={user.avatar || "/placeholder.svg"} alt={user.name} />
              <AvatarFallback>{user.name.charAt(0)}</AvatarFallback>
            </Avatar>
            <div>
              <CardTitle className="text-lg">{user.name}</CardTitle>
              <p className="text-sm text-muted-foreground">{user.status}</p>
            </div>
          </div>
        </CardHeader>
        <CardContent className="flex-1 flex flex-col">
          <ScrollArea className="flex-1 p-4 border rounded-md mb-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex items-start gap-3 mb-4 ${message.sender === "You" ? "justify-end" : ""}`}
              >
                {message.sender !== "You" && (
                  <Avatar>
                    <AvatarImage src={message.avatar || "/placeholder.svg"} alt={message.sender} />
                    <AvatarFallback>{message.sender.charAt(0)}</AvatarFallback>
                  </Avatar>
                )}
                <div className={`flex flex-col ${message.sender === "You" ? "items-end" : "items-start"}`}>
                  <div
                    className={`rounded-lg p-3 max-w-[70%] ${message.sender === "You" ? "bg-primary text-primary-foreground" : "bg-muted"}`}
                  >
                    <p className="text-sm">{message.content}</p>
                  </div>
                  <span className="text-xs text-muted-foreground mt-1">{message.time}</span>
                </div>
                {message.sender === "You" && (
                  <Avatar>
                    <AvatarImage src={message.avatar || "/placeholder.svg"} alt={message.sender} />
                    <AvatarFallback>{message.sender.charAt(0)}</AvatarFallback>
                  </Avatar>
                )}
              </div>
            ))}
          </ScrollArea>
          <div className="flex gap-2">
            <Input placeholder="Type your message..." className="flex-1" />
            <Button size="icon">
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
