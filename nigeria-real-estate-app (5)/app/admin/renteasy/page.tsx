import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { MoreHorizontal, PlusCircle } from "lucide-react"
import Link from "next/link"

interface Subscription {
  id: string
  userName: string
  plan: string
  status: "active" | "pending" | "cancelled" | "expired"
  startDate: string
  endDate: string
  price: string
}

async function getSubscriptions(): Promise<Subscription[]> {
  // Simulate fetching data
  return [
    {
      id: "sub_001",
      userName: "Ai<PERSON>",
      plan: "Premium",
      status: "active",
      startDate: "2024-01-01",
      endDate: "2025-01-01",
      price: "₦30,000",
    },
    {
      id: "sub_002",
      userName: "<PERSON>",
      plan: "Standard",
      status: "active",
      startDate: "2024-03-15",
      endDate: "2025-03-15",
      price: "₦15,000",
    },
    {
      id: "sub_003",
      userName: "Grace Obi",
      plan: "Basic",
      status: "expired",
      startDate: "2023-07-01",
      endDate: "2024-06-30",
      price: "₦5,000",
    },
    {
      id: "sub_004",
      userName: "David Eze",
      plan: "Premium",
      status: "pending",
      startDate: "2024-08-01",
      endDate: "2025-08-01",
      price: "₦30,000",
    },
  ]
}

export default async function AdminRentEasyPage() {
  const subscriptions = await getSubscriptions()

  return (
    <div className="p-4 md:p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">RentEasy Subscriptions</h1>
        <Button asChild>
          <Link href="/admin/renteasy/add">
            <PlusCircle className="mr-2 h-4 w-4" /> Add Subscription
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Subscriptions</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>User</TableHead>
                <TableHead>Plan</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Start Date</TableHead>
                <TableHead>End Date</TableHead>
                <TableHead>Price</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {subscriptions.map((sub) => (
                <TableRow key={sub.id}>
                  <TableCell className="font-medium">{sub.userName}</TableCell>
                  <TableCell>{sub.plan}</TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        sub.status === "active" ? "default" : sub.status === "pending" ? "secondary" : "destructive"
                      }
                    >
                      {sub.status.charAt(0).toUpperCase() + sub.status.slice(1)}
                    </Badge>
                  </TableCell>
                  <TableCell>{sub.startDate}</TableCell>
                  <TableCell>{sub.endDate}</TableCell>
                  <TableCell>{sub.price}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>View Details</DropdownMenuItem>
                        <DropdownMenuItem>Edit Subscription</DropdownMenuItem>
                        <DropdownMenuItem>Cancel Subscription</DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
