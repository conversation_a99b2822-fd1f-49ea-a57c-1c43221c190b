import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search } from "lucide-react"
import { PropertyCard } from "@/components/property-card"
import { PropertyMap } from "@/components/property-map"
import { RecentSearches } from "@/components/recent-searches"
import { RecommendedProperties } from "@/components/recommended-properties"

interface Property {
  id: string
  title: string
  location: string
  price: string
  priceType: string
  purpose: "rent" | "sale"
  type: string
  bedrooms: number
  bathrooms: number
  imageUrl: string
  coordinates: { lat: number; lng: number }
}

async function getProperties(): Promise<Property[]> {
  // Simulate fetching properties
  return [
    {
      id: "1",
      title: "Modern 3-Bedroom Apartment",
      location: "Victoria Island, Lagos",
      price: "₦2,500,000",
      priceType: "year",
      purpose: "rent",
      type: "Apartment",
      bedrooms: 3,
      bathrooms: 2,
      imageUrl: "/placeholder.svg?height=200&width=300",
      coordinates: { lat: 6.4281, lng: 3.4219 },
    },
    {
      id: "2",
      title: "Luxury 5-Bedroom Duplex",
      location: "Ikoyi, Lagos",
      price: "₦180,000,000",
      priceType: "sale",
      purpose: "sale",
      type: "Duplex",
      bedrooms: 5,
      bathrooms: 6,
      imageUrl: "/placeholder.svg?height=200&width=300",
      coordinates: { lat: 6.4531, lng: 3.4 },
    },
    {
      id: "3",
      title: "Spacious 2-Bedroom Flat",
      location: "Yaba, Lagos",
      price: "₦1,200,000",
      priceType: "year",
      purpose: "rent",
      type: "Flat",
      bedrooms: 2,
      bathrooms: 2,
      imageUrl: "/placeholder.svg?height=200&width=300",
      coordinates: { lat: 6.5175, lng: 3.3941 },
    },
    {
      id: "4",
      title: "Commercial Land",
      location: "Lekki, Lagos",
      price: "₦90,000,000",
      priceType: "sale",
      purpose: "sale",
      type: "Land",
      bedrooms: 0,
      bathrooms: 0,
      imageUrl: "/placeholder.svg?height=200&width=300",
      coordinates: { lat: 6.4654, lng: 3.5875 },
    },
    {
      id: "5",
      title: "4-Bedroom Detached House",
      location: "Abuja, FCT",
      price: "₦75,000,000",
      priceType: "sale",
      purpose: "sale",
      type: "House",
      bedrooms: 4,
      bathrooms: 4,
      imageUrl: "/placeholder.svg?height=200&width=300",
      coordinates: { lat: 9.0765, lng: 7.3986 },
    },
    {
      id: "6",
      title: "Serviced Studio Apartment",
      location: "Port Harcourt, Rivers",
      price: "₦800,000",
      priceType: "year",
      purpose: "rent",
      type: "Apartment",
      bedrooms: 1,
      bathrooms: 1,
      imageUrl: "/placeholder.svg?height=200&width=300",
      coordinates: { lat: 4.7762, lng: 7.0132 },
    },
  ]
}

export default async function PropertiesPage() {
  const properties = await getProperties()

  return (
    <div className="p-4 md:p-8 lg:p-12">
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-center mb-4">Discover Your Next Property</h1>
        <p className="text-lg text-muted-foreground text-center max-w-2xl mx-auto">
          Explore a wide range of properties for rent and sale across Nigeria.
        </p>
      </div>

      {/* Search and Filter Section */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Find Your Property</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 items-end">
            <div className="space-y-2">
              <label htmlFor="location" className="text-sm font-medium">
                Location
              </label>
              <Input id="location" placeholder="e.g., Lagos, Abuja" />
            </div>
            <div className="space-y-2">
              <label htmlFor="purpose" className="text-sm font-medium">
                Purpose
              </label>
              <Select>
                <SelectTrigger id="purpose">
                  <SelectValue placeholder="Rent or Sale" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="rent">For Rent</SelectItem>
                  <SelectItem value="sale">For Sale</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label htmlFor="type" className="text-sm font-medium">
                Property Type
              </label>
              <Select>
                <SelectTrigger id="type">
                  <SelectValue placeholder="Any Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="apartment">Apartment</SelectItem>
                  <SelectItem value="house">House</SelectItem>
                  <SelectItem value="duplex">Duplex</SelectItem>
                  <SelectItem value="villa">Villa</SelectItem>
                  <SelectItem value="land">Land</SelectItem>
                  <SelectItem value="commercial">Commercial</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label htmlFor="min-price" className="text-sm font-medium">
                Min Price
              </label>
              <Input id="min-price" type="number" placeholder="Min Price" />
            </div>
            <div className="space-y-2">
              <label htmlFor="max-price" className="text-sm font-medium">
                Max Price
              </label>
              <Input id="max-price" type="number" placeholder="Max Price" />
            </div>
            <div className="space-y-2">
              <label htmlFor="bedrooms" className="text-sm font-medium">
                Bedrooms
              </label>
              <Input id="bedrooms" type="number" placeholder="Any" />
            </div>
            <div className="space-y-2">
              <label htmlFor="bathrooms" className="text-sm font-medium">
                Bathrooms
              </label>
              <Input id="bathrooms" type="number" placeholder="Any" />
            </div>
            <Button className="col-span-full md:col-span-1 lg:col-span-2 mt-4 md:mt-0">
              <Search className="h-4 w-4 mr-2" /> Search Properties
            </Button>
          </div>
        </CardContent>
      </Card>

      {/* Property Listings */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {properties.map((property) => (
          <PropertyCard key={property.id} property={property} />
        ))}
      </div>

      {/* Map View Section */}
      <Card className="mt-12">
        <CardHeader>
          <CardTitle>Properties on Map</CardTitle>
        </CardHeader>
        <CardContent>
          <PropertyMap properties={properties} />
        </CardContent>
      </Card>

      {/* Recent Searches */}
      <RecentSearches className="mt-12" />

      {/* Recommended Properties */}
      <RecommendedProperties className="mt-12" />
    </div>
  )
}
