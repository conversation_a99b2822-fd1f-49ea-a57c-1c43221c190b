import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardDescription } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ShieldOff, Home } from "lucide-react"
import Link from "next/link"
import { LoginPrompt } from "@/components/login-prompt"
import { auth } from "@/lib/auth"

export default async function UnauthorizedPage() {
  const session = await auth()

  return (
    <div className="flex items-center justify-center min-h-[calc(100vh-64px)] p-4">
      <Card className="w-full max-w-lg text-center">
        <CardHeader>
          <CardTitle className="text-4xl font-bold text-destructive flex items-center justify-center gap-3">
            <ShieldOff className="h-10 w-10" /> Access Denied
          </CardTitle>
          <CardDescription className="text-lg mt-4">You do not have permission to view this page.</CardDescription>
        </CardHeader>
        <CardContent className="space-y-6">
          <p className="text-muted-foreground">
            This section of the application is restricted. Please ensure you are logged in with the correct user role.
          </p>
          {session?.user ? (
            <div className="space-y-4">
              <p className="text-lg font-semibold">
                Logged in as: {session.user.email} ({session.user.role})
              </p>
              <Button asChild>
                <Link href="/">
                  <Home className="mr-2 h-4 w-4" /> Go to Home
                </Link>
              </Button>
            </div>
          ) : (
            <LoginPrompt />
          )}
        </CardContent>
      </Card>
    </div>
  )
}
