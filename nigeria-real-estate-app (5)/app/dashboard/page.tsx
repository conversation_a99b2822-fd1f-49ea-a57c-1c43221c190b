import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>it<PERSON> } from "@/components/ui/card"
import { MessageSquare, Bookmark } from "lucide-react"
import { auth } from "@/lib/auth" // Import auth from the new file
import { redirect } from "next/navigation"
import { LoginPrompt } from "@/components/login-prompt"

async function getUserDashboardData() {
  // Simulate fetching user-specific data
  return {
    totalSavedProperties: 15,
    unreadMessages: 3,
    recentActivity: [
      { id: 1, type: "view", description: "Viewed 'Luxury 4-Bedroom Duplex'", date: "2024-07-20" },
      { id: 2, type: "message", description: "New message from Agent <PERSON>", date: "2024-07-19" },
      { id: 3, type: "save", description: "Saved 'Spacious 2-Bedroom Flat'", date: "2024-07-18" },
    ],
    recommendedPropertiesCount: 8,
  }
}

export default async function DashboardPage() {
  const session = await auth()

  if (!session?.user) {
    return (
      <div className="flex items-center justify-center min-h-[calc(100vh-64px)] p-4">
        <LoginPrompt />
      </div>
    )
  }

  // Redirect based on role if they try to access /dashboard directly
  if (session.user.role === "manager") {
    redirect("/manager/dashboard")
  }
  if (session.user.role === "landlord") {
    redirect("/landlord/dashboard")
  }
  if (session.user.role === "admin") {
    redirect("/admin/renteasy") // Or a general admin dashboard
  }

  const dashboardData = await getUserDashboardData()

  return (
    <div className="p-4 md:p-6 space-y-6">
      <h1 className="text-3xl font-bold">Welcome, {session.user.name || session.user.email}!</h1>

      <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Saved Properties</CardTitle>
            <Bookmark className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{dashboardData.totalSavedProperties}</div>
            <p className="text-xs text-muted-foreground">Your favorite listings</p>
          </CardContent>
        </Card>
        <Card>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">Unread Messages</CardTitle>
            <MessageSquare className\
