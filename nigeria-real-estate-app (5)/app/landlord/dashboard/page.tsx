"use client"

import { useState, useEffect } from "react"
import {
  Home,
  Users,
  TrendingUp,
  FileText,
  Settings,
  CheckCircle,
  Shield,
  DollarSign,
  Wrench,
  ArrowUpDown,
  MoreHorizontal,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { useSession } from "next-auth/react" // Import useSession
import { useRouter } from "next/navigation" // Import useRouter
import { ConsistentHeader } from "@/components/ui/consistent-header"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import Link from "next/link"

const landlordInfo = {
  name: "Chief <PERSON><PERSON><PERSON><PERSON>",
  title: "Property Owner",
  avatar: "/placeholder.svg?height=60&width=60",
  totalProperties: 12,
  totalMonthlyRent: 15750000, // ₦15.75M
  totalTenants: 28,
  occupancyRate: 85,
}

const summaryStats = [
  {
    title: "Active Properties",
    value: landlordInfo.totalProperties.toString(),
    subtitle: `${Math.round(landlordInfo.occupancyRate)}% occupied`,
    icon: Home,
    color: "text-blue-600",
    bgColor: "bg-blue-50",
  },
  {
    title: "Monthly Rent",
    value: `₦${(landlordInfo.totalMonthlyRent / 1000000).toFixed(1)}M`,
    subtitle: "Expected income",
    icon: TrendingUp,
    color: "text-green-600",
    bgColor: "bg-green-50",
  },
  {
    title: "Current Tenants",
    value: landlordInfo.totalTenants.toString(),
    subtitle: "Across all properties",
    icon: Users,
    color: "text-purple-600",
    bgColor: "bg-purple-50",
  },
]

const propertiesData = [
  {
    id: 1,
    name: "Sunrise Apartments Block A",
    type: "2-Bedroom Flats",
    location: "Victoria Island, Lagos",
    monthlyRent: 2500000,
    status: "Occupied",
    tenantName: "Mr. & Mrs. Adeyemi",
    manager: {
      name: "Adebayo Properties",
      avatar: "/placeholder.svg?height=32&width=32",
      phone: "+234 ************",
    },
    inquiries: 12,
    lastRented: "2024-01-15",
    image: "/placeholder.svg?height=80&width=80",
    occupancyRate: 100,
  },
  {
    id: 2,
    name: "Golden Heights Complex",
    type: "3-Bedroom Duplex",
    location: "Lekki Phase 1, Lagos",
    monthlyRent: 3200000,
    status: "Occupied",
    tenantName: "Dr. Chukwu Family",
    manager: {
      name: "Prime Estate Managers",
      avatar: "/placeholder.svg?height=32&width=32",
      phone: "+234 ************",
    },
    inquiries: 8,
    lastRented: "2023-11-20",
    image: "/placeholder.svg?height=80&width=80",
    occupancyRate: 100,
  },
  {
    id: 3,
    name: "Heritage Court",
    type: "Self-Contained",
    location: "Wuse 2, Abuja",
    monthlyRent: 1800000,
    status: "Vacant",
    tenantName: null,
    manager: {
      name: "Capital Property Solutions",
      avatar: "/placeholder.svg?height=32&width=32",
      phone: "+234 ************",
    },
    inquiries: 15,
    lastRented: "2023-12-10",
    image: "/placeholder.svg?height=80&width=80",
    occupancyRate: 0,
  },
  {
    id: 4,
    name: "Royal Gardens Estate",
    type: "4-Bedroom Detached",
    location: "Maitama, Abuja",
    monthlyRent: 4500000,
    status: "Occupied",
    tenantName: "Ambassador Johnson",
    manager: {
      name: "Elite Property Management",
      avatar: "/placeholder.svg?height=32&width=32",
      phone: "+234 ************",
    },
    inquiries: 6,
    lastRented: "2024-02-01",
    image: "/placeholder.svg?height=80&width=80",
    occupancyRate: 100,
  },
  {
    id: 5,
    name: "Emerald Towers",
    type: "Mini Flat",
    location: "Ikeja, Lagos",
    monthlyRent: 1200000,
    status: "Occupied",
    tenantName: "Miss Fatima Bello",
    manager: {
      name: "Adebayo Properties",
      avatar: "/placeholder.svg?height=32&width=32",
      phone: "+234 ************",
    },
    inquiries: 20,
    lastRented: "2024-01-08",
    image: "/placeholder.svg?height=80&width=80",
    occupancyRate: 100,
  },
  {
    id: 6,
    name: "Serenity Homes",
    type: "1-Bedroom Flat",
    location: "GRA, Port Harcourt",
    monthlyRent: 1500000,
    status: "Vacant",
    tenantName: null,
    manager: {
      name: "Rivers Property Hub",
      avatar: "/placeholder.svg?height=32&width=32",
      phone: "+234 ************",
    },
    inquiries: 9,
    lastRented: "2023-10-15",
    image: "/placeholder.svg?height=80&width=80",
    occupancyRate: 0,
  },
]

const conversations = [
  {
    id: 1,
    propertyId: 1,
    propertyName: "Sunrise Apartments Block A",
    participants: {
      tenant: {
        name: "Mr. & Mrs. Adeyemi",
        avatar: "/placeholder.svg?height=32&width=32",
      },
      manager: {
        name: "Adebayo Properties",
        avatar: "/placeholder.svg?height=32&width=32",
      },
    },
    lastMessage: {
      content: "The air conditioning in the master bedroom needs servicing",
      sender: "tenant",
      timestamp: "2 hours ago",
      unread: true,
    },
    messages: [
      {
        id: 1,
        sender: "tenant",
        content: "Good morning. The air conditioning in the master bedroom is not cooling properly.",
        timestamp: "9:30 AM",
        date: "Today",
      },
      {
        id: 2,
        sender: "manager",
        content:
          "Good morning Mr. Adeyemi. I'll arrange for a technician to check it today. The landlord has been notified.",
        timestamp: "9:45 AM",
        date: "Today",
      },
      {
        id: 3,
        sender: "tenant",
        content: "Thank you. What time should we expect the technician?",
        timestamp: "10:15 AM",
        date: "Today",
      },
    ],
  },
  {
    id: 2,
    propertyId: 4,
    propertyName: "Royal Gardens Estate",
    participants: {
      tenant: {
        name: "Ambassador Johnson",
        avatar: "/placeholder.svg?height=32&width=32",
      },
      manager: {
        name: "Elite Property Management",
        avatar: "/placeholder.svg?height=32&width=32",
      },
    },
    lastMessage: {
      content: "Rent payment for March has been processed successfully",
      sender: "manager",
      timestamp: "1 day ago",
      unread: false,
    },
    messages: [],
  },
  {
    id: 3,
    propertyId: 3,
    propertyName: "Heritage Court",
    participants: {
      tenant: null,
      manager: {
        name: "Capital Property Solutions",
        avatar: "/placeholder.svg?height=32&width=32",
      },
    },
    lastMessage: {
      content: "New viewing scheduled for this weekend",
      sender: "manager",
      timestamp: "3 days ago",
      unread: false,
    },
    messages: [],
  },
]

const documents = [
  {
    id: 1,
    propertyId: 1,
    propertyName: "Sunrise Apartments Block A",
    type: "Lease Agreement",
    fileName: "Lease_Agreement_Adeyemi_2024.pdf",
    uploadDate: "2024-01-15",
    expiryDate: "2025-01-14",
    status: "Active",
    size: "2.3 MB",
    category: "Legal",
  },
  {
    id: 2,
    propertyId: 1,
    propertyName: "Sunrise Apartments Block A",
    type: "Property Certificate",
    fileName: "Certificate_of_Occupancy.pdf",
    uploadDate: "2023-12-01",
    expiryDate: null,
    status: "Valid",
    size: "1.8 MB",
    category: "Legal",
  },
  {
    id: 3,
    propertyId: 2,
    propertyName: "Golden Heights Complex",
    type: "Insurance Policy",
    fileName: "Property_Insurance_2024.pdf",
    uploadDate: "2024-01-01",
    expiryDate: "2024-12-31",
    status: "Active",
    size: "1.2 MB",
    category: "Insurance",
  },
  {
    id: 4,
    propertyId: 4,
    propertyName: "Royal Gardens Estate",
    type: "Tenancy Agreement",
    fileName: "Tenancy_Agreement_Johnson.pdf",
    uploadDate: "2024-02-01",
    expiryDate: "2025-01-31",
    status: "Active",
    size: "2.1 MB",
    category: "Legal",
  },
  {
    id: 5,
    propertyId: 3,
    propertyName: "Heritage Court",
    type: "Maintenance Report",
    fileName: "Maintenance_Report_Q1_2024.pdf",
    uploadDate: "2024-03-15",
    expiryDate: null,
    status: "Completed",
    size: "856 KB",
    category: "Maintenance",
  },
  {
    id: 6,
    propertyId: 2,
    propertyName: "Golden Heights Complex",
    type: "Property Valuation",
    fileName: "Property_Valuation_2024.pdf",
    uploadDate: "2024-02-20",
    expiryDate: "2025-02-19",
    status: "Valid",
    size: "3.4 MB",
    category: "Financial",
  },
  {
    id: 7,
    propertyId: 5,
    propertyName: "Emerald Towers",
    type: "Lease Agreement",
    fileName: "Lease_Agreement_Bello_2024.pdf",
    uploadDate: "2024-01-08",
    expiryDate: "2025-01-07",
    status: "Active",
    size: "2.0 MB",
    category: "Legal",
  },
  {
    id: 8,
    propertyId: 1,
    propertyName: "Sunrise Apartments Block A",
    type: "Inspection Report",
    fileName: "Annual_Inspection_2024.pdf",
    uploadDate: "2024-03-01",
    expiryDate: null,
    status: "Completed",
    size: "1.5 MB",
    category: "Maintenance",
  },
]

const formatCurrency = (amount: number) => {
  if (amount >= 1000000) {
    return `₦${(amount / 1000000).toFixed(1)}M`
  }
  return `₦${amount.toLocaleString()}`
}

const formatDate = (dateString: string) => {
  const date = new Date(dateString)
  return date.toLocaleDateString("en-GB", {
    day: "numeric",
    month: "short",
    year: "numeric",
  })
}

const getDocumentIcon = (type: string) => {
  switch (type.toLowerCase()) {
    case "lease agreement":
    case "tenancy agreement":
      return <FileText className="h-5 w-5 text-blue-600" />
    case "property certificate":
      return <CheckCircle className="h-5 w-5 text-green-600" />
    case "insurance policy":
      return <Shield className="h-5 w-5 text-purple-600" />
    case "maintenance report":
    case "inspection report":
      return <Settings className="h-5 w-5 text-orange-600" />
    case "property valuation":
      return <TrendingUp className="h-5 w-5 text-indigo-600" />
    default:
      return <FileText className="h-5 w-5 text-gray-600" />
  }
}

const getDocumentStatusColor = (status: string) => {
  switch (status.toLowerCase()) {
    case "active":
    case "valid":
      return "bg-green-100 text-green-700"
    case "expiring soon":
      return "bg-yellow-100 text-yellow-700"
    case "expired":
      return "bg-red-100 text-red-700"
    case "completed":
      return "bg-blue-100 text-blue-700"
    default:
      return "bg-gray-100 text-gray-700"
  }
}

const dashboardOverview = {
  totalProperties: 5,
  occupiedUnits: 4,
  monthlyIncome: "₦12,500,000",
  pendingMaintenance: 2,
}

const properties = [
  {
    id: "prop1",
    title: "Modern 3-Bedroom Apartment",
    location: "Victoria Island, Lagos",
    status: "occupied",
    tenant: "Alice Johnson",
    nextRentDue: "2024-07-01",
  },
  {
    id: "prop2",
    title: "Spacious 5-Bedroom Duplex",
    location: "Lekki Phase 1, Lagos",
    status: "available",
    tenant: "-",
    nextRentDue: "-",
  },
  {
    id: "prop3",
    title: "Commercial Office Space",
    location: "Central Business District, Abuja",
    status: "occupied",
    tenant: "Tech Solutions Ltd.",
    nextRentDue: "2024-07-05",
  },
]

const recentMaintenance = [
  {
    id: "maint1",
    property: "Apartment 3B, Victoria Island",
    issue: "Leaky Faucet in Kitchen",
    status: "pending", // pending, in-progress, completed
    reportedBy: "Alice Johnson",
    date: "2024-06-28",
  },
  {
    id: "maint2",
    property: "Duplex 5, Lekki Phase 1",
    issue: "AC not cooling",
    status: "completed",
    reportedBy: "Bob Williams",
    date: "2024-06-20",
  },
]

const getStatusBadge = (status: string) => {
  switch (status) {
    case "occupied":
      return <Badge className="bg-blue-500 hover:bg-blue-600 text-white">Occupied</Badge>
    case "available":
      return <Badge className="bg-green-500 hover:bg-green-600 text-white">Available</Badge>
    case "pending":
      return <Badge variant="outline">Pending</Badge>
    case "completed":
      return <Badge className="bg-green-500 hover:bg-green-600 text-white">Completed</Badge>
    default:
      return <Badge variant="secondary">{status}</Badge>
  }
}

export default function LandlordDashboard() {
  const { data: session, status } = useSession()
  const router = useRouter()

  const [activeTab, setActiveTab] = useState("dashboard")
  const [selectedDocuments, setSelectedDocuments] = useState<number[]>([])
  const [isSelectMode, setIsSelectMode] = useState(false)
  const [sortBy, setSortBy] = useState("uploadDate")
  const [filterBy, setFilterBy] = useState("all")
  const [groupBy, setGroupBy] = useState("none")

  // Client-side protection: Redirect if not authenticated or wrong role
  useEffect(() => {
    if (status === "loading") return // Do nothing while loading session
    if (status === "unauthenticated" || session?.user.role !== "landlord") {
      router.push("/unauthorized") // Redirect to unauthorized page
    }
  }, [session, status, router])

  if (status === "loading" || status === "unauthenticated" || session?.user.role !== "landlord") {
    return (
      <div className="flex min-h-screen items-center justify-center bg-gray-50">
        <p className="text-gray-600">Loading or redirecting...</p>
      </div>
    )
  }

  const toggleDocumentSelection = (documentId: number) => {
    setSelectedDocuments((current) =>
      current.includes(documentId) ? current.filter((id) => id !== documentId) : [...current, documentId],
    )
  }

  const selectAllDocuments = () => {
    const filteredDocs = getFilteredAndSortedDocuments()
    setSelectedDocuments(filteredDocs.map((doc) => doc.id))
  }

  const clearSelection = () => {
    setSelectedDocuments([])
    setIsSelectMode(false)
  }

  const getFilteredAndSortedDocuments = () => {
    let filtered = documents

    if (filterBy !== "all") {
      filtered = documents.filter((doc) => doc.category.toLowerCase() === filterBy.toLowerCase())
    }

    return filtered.sort((a, b) => {
      switch (sortBy) {
        case "name":
          return a.fileName.localeCompare(b.fileName)
        case "type":
          return a.type.localeCompare(b.type)
        case "property":
          return a.propertyName.localeCompare(b.propertyName)
        case "size":
          return Number.parseFloat(a.size) - Number.parseFloat(b.size)
        case "uploadDate":
        default:
          return new Date(b.uploadDate).getTime() - new Date(a.uploadDate).getTime()
      }
    })
  }

  const getGroupedDocuments = () => {
    const filteredDocs = getFilteredAndSortedDocuments()

    if (groupBy === "property") {
      const grouped = filteredDocs.reduce(
        (acc, doc) => {
          const key = doc.propertyName
          if (!acc[key]) acc[key] = []
          acc[key].push(doc)
          return acc
        },
        {} as Record<string, typeof documents>,
      )
      return grouped
    } else if (groupBy === "category") {
      const grouped = filteredDocs.reduce(
        (acc, doc) => {
          const key = doc.category
          if (!acc[key]) acc[key] = []
          acc[key].push(doc)
          return acc
        },
        {} as Record<string, typeof documents>,
      )
      return grouped
    }

    return { "All Documents": filteredDocs }
  }

  const handleBulkDownload = () => {
    const selectedDocs = documents.filter((doc) => selectedDocuments.includes(doc.id))
    const totalSize = selectedDocs.reduce((acc, doc) => {
      const size = Number.parseFloat(doc.size.replace(/[^\d.]/g, ""))
      return acc + size
    }, 0)

    alert(
      `Preparing download of ${selectedDocs.length} documents (${totalSize.toFixed(1)} MB)...\n\nFiles will be compressed into a ZIP archive.`,
    )
    clearSelection()
  }

  const handleBulkOrganize = (action: string) => {
    const selectedDocs = documents.filter((doc) => selectedDocuments.includes(doc.id))
    switch (action) {
      case "archive":
        alert(`Archiving ${selectedDocs.length} documents...\n\nDocuments will be moved to the archive folder.`)
        break
      case "export":
        alert(
          `Exporting ${selectedDocs.length} documents to PDF report...\n\nA comprehensive report will be generated.`,
        )
        break
      case "share":
        alert(
          `Sharing ${selectedDocs.length} documents with property managers...\n\nManagers will receive secure access links.`,
        )
        break
      case "delete":
        alert(`Are you sure you want to delete ${selectedDocs.length} documents?\n\nThis action cannot be undone.`)
        break
    }
    clearSelection()
  }

  return (
    <div className="flex h-full flex-col">
      <ConsistentHeader title="Landlord Dashboard" />
      <div className="p-4 md:p-6 lg:p-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Properties</CardTitle>
              <Home className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dashboardOverview.totalProperties}</div>
              <p className="text-xs text-gray-500">Units owned</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Occupied Units</CardTitle>
              <Users className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dashboardOverview.occupiedUnits}</div>
              <p className="text-xs text-gray-500">Currently rented out</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Monthly Income</CardTitle>
              <DollarSign className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dashboardOverview.monthlyIncome}</div>
              <p className="text-xs text-gray-500">Expected gross income</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Pending Maintenance</CardTitle>
              <Wrench className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dashboardOverview.pendingMaintenance}</div>
              <p className="text-xs text-gray-500">Issues awaiting resolution</p>
            </CardContent>
          </Card>
        </div>

        <Card className="mb-6">
          <CardHeader>
            <CardTitle>My Properties</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Property Title</TableHead>
                  <TableHead>Location</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead>Tenant</TableHead>
                  <TableHead>
                    <Button variant="ghost">
                      Next Rent Due <ArrowUpDown className="ml-2 h-4 w-4" />
                    </Button>
                  </TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {properties.map((property) => (
                  <TableRow key={property.id}>
                    <TableCell className="font-medium">{property.title}</TableCell>
                    <TableCell>{property.location}</TableCell>
                    <TableCell>{getStatusBadge(property.status)}</TableCell>
                    <TableCell>{property.tenant}</TableCell>
                    <TableCell>{property.nextRentDue}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem asChild>
                            <Link href={`/property/${property.id}`}>View Details</Link>
                          </DropdownMenuItem>
                          <DropdownMenuItem>View Tenant</DropdownMenuItem>
                          <DropdownMenuItem>View Payments</DropdownMenuItem>
                          <DropdownMenuItem>Report Maintenance</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>

        <Card>
          <CardHeader>
            <CardTitle>Recent Maintenance Requests</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Property</TableHead>
                  <TableHead>Issue</TableHead>
                  <TableHead>Reported By</TableHead>
                  <TableHead>Date</TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recentMaintenance.map((maint) => (
                  <TableRow key={maint.id}>
                    <TableCell className="font-medium">{maint.property}</TableCell>
                    <TableCell>{maint.issue}</TableCell>
                    <TableCell>{maint.reportedBy}</TableCell>
                    <TableCell>{maint.date}</TableCell>
                    <TableCell>{getStatusBadge(maint.status)}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>View Details</DropdownMenuItem>
                          <DropdownMenuItem>Update Status</DropdownMenuItem>
                          <DropdownMenuItem>Assign Vendor</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
