"use client"

import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search } from "lucide-react"
import { PropertyCard } from "@/components/property-card"
import { PropertyMap } from "@/components/property-map"
import { RecentSearches } from "@/components/recent-searches"
import { RecommendedProperties } from "@/components/recommended-properties"
import { usePropertySearch } from "@/hooks/use-property-search"

interface Property {
  id: string
  title: string
  location: string
  price: string
  priceType: string
  purpose: "rent" | "sale"
  type: string
  bedrooms: number
  bathrooms: number
  imageUrl: string
  coordinates: { lat: number; lng: number }
}

async function getSearchResults(): Promise<Property[]> {
  // Simulate fetching properties based on search criteria
  // This would typically come from an API call using the search parameters
  return [
    {
      id: "1",
      title: "Modern 3-Bedroom Apartment",
      location: "Victoria Island, Lagos",
      price: "₦2,500,000",
      priceType: "year",
      purpose: "rent",
      type: "Apartment",
      bedrooms: 3,
      bathrooms: 2,
      imageUrl: "/placeholder.svg?height=200&width=300",
      coordinates: { lat: 6.4281, lng: 3.4219 },
    },
    {
      id: "2",
      title: "Luxury 5-Bedroom Duplex",
      location: "Ikoyi, Lagos",
      price: "₦180,000,000",
      priceType: "sale",
      purpose: "sale",
      type: "Duplex",
      bedrooms: 5,
      bathrooms: 6,
      imageUrl: "/placeholder.svg?height=200&width=300",
      coordinates: { lat: 6.4531, lng: 3.4 },
    },
    {
      id: "3",
      title: "Spacious 2-Bedroom Flat",
      location: "Yaba, Lagos",
      price: "₦1,200,000",
      priceType: "year",
      purpose: "rent",
      type: "Flat",
      bedrooms: 2,
      bathrooms: 2,
      imageUrl: "/placeholder.svg?height=200&width=300",
      coordinates: { lat: 6.5175, lng: 3.3941 },
    },
    {
      id: "4",
      title: "Commercial Land",
      location: "Lekki, Lagos",
      price: "₦90,000,000",
      priceType: "sale",
      purpose: "sale",
      type: "Land",
      bedrooms: 0,
      bathrooms: 0,
      imageUrl: "/placeholder.svg?height=200&width=300",
      coordinates: { lat: 6.4654, lng: 3.5875 },
    },
  ]
}

export default async function SearchPage() {
  const { searchParams, handleSearchChange, handleFilterChange, handleSearchSubmit } = usePropertySearch()
  const properties = await getSearchResults() // In a real app, this would use searchParams

  return (
    <div className="p-4 md:p-8 lg:p-12">
      <div className="mb-8">
        <h1 className="text-4xl font-bold text-center mb-4">Advanced Property Search</h1>
        <p className="text-lg text-muted-foreground text-center max-w-2xl mx-auto">
          Find your ideal property by applying detailed filters and search criteria.
        </p>
      </div>

      {/* Search and Filter Section */}
      <Card className="mb-8">
        <CardHeader>
          <CardTitle>Refine Your Search</CardTitle>
        </CardHeader>
        <CardContent>
          <form
            onSubmit={handleSearchSubmit}
            className="grid grid-cols-1 md:grid-cols-3 lg:grid-cols-5 gap-4 items-end"
          >
            <div className="space-y-2">
              <label htmlFor="location" className="text-sm font-medium">
                Location
              </label>
              <Input
                id="location"
                placeholder="e.g., Lagos, Abuja"
                value={searchParams.location || ""}
                onChange={handleSearchChange}
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="purpose" className="text-sm font-medium">
                Purpose
              </label>
              <Select
                value={searchParams.purpose || "any"}
                onValueChange={(value) => handleFilterChange("purpose", value)}
              >
                <SelectTrigger id="purpose">
                  <SelectValue placeholder="Rent or Sale" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="any">Any</SelectItem>
                  <SelectItem value="rent">For Rent</SelectItem>
                  <SelectItem value="sale">For Sale</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label htmlFor="type" className="text-sm font-medium">
                Property Type
              </label>
              <Select value={searchParams.type || "any"} onValueChange={(value) => handleFilterChange("type", value)}>
                <SelectTrigger id="type">
                  <SelectValue placeholder="Any Type" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="any">Any</SelectItem>
                  <SelectItem value="apartment">Apartment</SelectItem>
                  <SelectItem value="house">House</SelectItem>
                  <SelectItem value="duplex">Duplex</SelectItem>
                  <SelectItem value="villa">Villa</SelectItem>
                  <SelectItem value="land">Land</SelectItem>
                  <SelectItem value="commercial">Commercial</SelectItem>
                </SelectContent>
              </Select>
            </div>
            <div className="space-y-2">
              <label htmlFor="minPrice" className="text-sm font-medium">
                Min Price
              </label>
              <Input
                id="minPrice"
                type="number"
                placeholder="Min Price"
                value={searchParams.minPrice || ""}
                onChange={handleSearchChange}
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="maxPrice" className="text-sm font-medium">
                Max Price
              </label>
              <Input
                id="maxPrice"
                type="number"
                placeholder="Max Price"
                value={searchParams.maxPrice || ""}
                onChange={handleSearchChange}
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="bedrooms" className="text-sm font-medium">
                Bedrooms
              </label>
              <Input
                id="bedrooms"
                type="number"
                placeholder="Any"
                value={searchParams.bedrooms || ""}
                onChange={handleSearchChange}
              />
            </div>
            <div className="space-y-2">
              <label htmlFor="bathrooms" className="text-sm font-medium">
                Bathrooms
              </label>
              <Input
                id="bathrooms"
                type="number"
                placeholder="Any"
                value={searchParams.bathrooms || ""}
                onChange={handleSearchChange}
              />
            </div>
            <Button type="submit" className="col-span-full md:col-span-1 lg:col-span-2 mt-4 md:mt-0">
              <Search className="h-4 w-4 mr-2" /> Apply Filters
            </Button>
          </form>
        </CardContent>
      </Card>

      {/* Search Results */}
      <h2 className="text-3xl font-bold mb-6">Search Results ({properties.length})</h2>
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {properties.map((property) => (
          <PropertyCard key={property.id} property={property} />
        ))}
      </div>

      {/* Map View Section */}
      <Card className="mt-12">
        <CardHeader>
          <CardTitle>Properties on Map</CardTitle>
        </CardHeader>
        <CardContent>
          <PropertyMap properties={properties} />
        </CardContent>
      </Card>

      {/* Recent Searches */}
      <RecentSearches className="mt-12" />

      {/* Recommended Properties */}
      <RecommendedProperties className="mt-12" />
    </div>
  )
}
