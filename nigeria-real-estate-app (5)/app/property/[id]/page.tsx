import { AvatarFallback } from "@/components/ui/avatar"
import { AvatarImage } from "@/components/ui/avatar"
import { Avatar } from "@/components/ui/avatar"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { MapPin, Bed, Bath, Ruler, Phone, Mail, Heart, Share2 } from "lucide-react"
import Image from "next/image"
import { PropertyMap } from "@/components/property-map"
import { RecommendedProperties } from "@/components/recommended-properties"
import { PropertyComparison } from "@/components/property-comparison"

interface PropertyDetails {
  id: string
  title: string
  description: string
  location: string
  coordinates: { lat: number; lng: number }
  price: string
  priceType: string
  purpose: "rent" | "sale"
  type: string
  bedrooms: number
  bathrooms: number
  size: number
  features: string[]
  amenities: string[]
  images: string[]
  agent: {
    name: string
    phone: string
    email: string
    avatar: string
  }
}

async function getPropertyDetails(id: string): Promise<PropertyDetails> {
  // Simulate fetching data
  return {
    id,
    title: "Luxurious 5-Bedroom Villa with Private Pool",
    description:
      "Experience unparalleled luxury in this exquisite 5-bedroom villa located in the prestigious Banana Island. Boasting spacious interiors, modern amenities, a private swimming pool, and lush gardens, this property offers the ultimate in comfort and elegance. Perfect for families seeking a serene and upscale living environment.",
    location: "Banana Island, Ikoyi, Lagos",
    coordinates: { lat: 6.4314, lng: 3.4406 },
    price: "₦450,000,000",
    priceType: "sale",
    purpose: "sale",
    type: "Villa",
    bedrooms: 5,
    bathrooms: 6,
    size: 800, // in square meters
    features: ["Private Pool", "Garden", "24/7 Security", "Smart Home System", "Generator", "Parking"],
    amenities: ["Gym", "Cinema Room", "Staff Quarters", "Waterfront View"],
    images: [
      "/placeholder.svg?height=600&width=800",
      "/placeholder.svg?height=600&width=800",
      "/placeholder.svg?height=600&width=800",
      "/placeholder.svg?height=600&width=800",
      "/placeholder.svg?height=600&width=800",
    ],
    agent: {
      name: "Grace Adebayo",
      phone: "+234 ************",
      email: "<EMAIL>",
      avatar: "/placeholder.svg?height=40&width=40",
    },
  }
}

export default async function PropertyDetailPage({ params }: { params: { id: string } }) {
  const property = await getPropertyDetails(params.id)

  return (
    <div className="p-4 md:p-8 lg:p-12 grid grid-cols-1 lg:grid-cols-3 gap-8">
      <div className="lg:col-span-2 space-y-8">
        {/* Property Images Carousel */}
        <Card>
          <CardContent className="p-0">
            <Carousel className="w-full">
              <CarouselContent>
                {property.images.map((image, index) => (
                  <CarouselItem key={index}>
                    <div className="relative h-[400px] w-full">
                      <Image
                        src={image || "/placeholder.svg"}
                        alt={`${property.title} image ${index + 1}`}
                        fill
                        style={{ objectFit: "cover" }}
                        className="rounded-lg"
                      />
                    </div>
                  </CarouselItem>
                ))}
              </CarouselContent>
              <CarouselPrevious className="ml-4" />
              <CarouselNext className="mr-4" />
            </Carousel>
          </CardContent>
        </Card>

        {/* Property Overview */}
        <Card>
          <CardHeader>
            <CardTitle className="text-3xl font-bold">{property.title}</CardTitle>
            <p className="text-2xl font-semibold text-primary">
              {property.price} {property.purpose === "rent" ? `/${property.priceType}` : ""}
            </p>
            <div className="flex items-center text-muted-foreground">
              <MapPin className="h-5 w-5 mr-2" />
              <span>{property.location}</span>
            </div>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="flex items-center gap-4 text-lg">
              <div className="flex items-center">
                <Bed className="h-5 w-5 mr-1" /> {property.bedrooms} Beds
              </div>
              <div className="flex items-center">
                <Bath className="h-5 w-5 mr-1" /> {property.bathrooms} Baths
              </div>
              <div className="flex items-center">
                <Ruler className="h-5 w-5 mr-1" /> {property.size} sqm
              </div>
            </div>
            <p className="text-muted-foreground">{property.description}</p>
            <Separator />
            <div>
              <h3 className="text-xl font-semibold mb-3">Features</h3>
              <div className="flex flex-wrap gap-2">
                {property.features.map((feature, index) => (
                  <Badge key={index} variant="secondary">
                    {feature}
                  </Badge>
                ))}
              </div>
            </div>
            <div>
              <h3 className="text-xl font-semibold mb-3">Amenities</h3>
              <div className="flex flex-wrap gap-2">
                {property.amenities.map((amenity, index) => (
                  <Badge key={index} variant="outline">
                    {amenity}
                  </Badge>
                ))}
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Property Location on Map */}
        <Card>
          <CardHeader>
            <CardTitle>Location</CardTitle>
          </CardHeader>
          <CardContent>
            <PropertyMap lat={property.coordinates.lat} lng={property.coordinates.lng} />
          </CardContent>
        </Card>

        {/* Property Comparison */}
        <PropertyComparison currentPropertyId={property.id} />
      </div>

      {/* Agent Contact & Actions */}
      <div className="lg:col-span-1 space-y-8">
        <Card>
          <CardHeader>
            <CardTitle>Contact Agent</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col items-center text-center">
            <Avatar className="h-20 w-20 mb-4">
              <AvatarImage src={property.agent.avatar || "/placeholder.svg"} alt={property.agent.name} />
              <AvatarFallback>{property.agent.name.charAt(0)}</AvatarFallback>
            </Avatar>
            <h3 className="text-xl font-semibold">{property.agent.name}</h3>
            <p className="text-muted-foreground mb-4">Real Estate Agent</p>
            <Button className="w-full mb-2">
              <Phone className="h-4 w-4 mr-2" /> Call Agent
            </Button>
            <Button variant="outline" className="w-full bg-transparent">
              <Mail className="h-4 w-4 mr-2" /> Email Agent
            </Button>
          </CardContent>
        </Card>

        {/* Quick Actions */}
        <Card>
          <CardHeader>
            <CardTitle>Actions</CardTitle>
          </CardHeader>
          <CardContent className="flex flex-col gap-3">
            <Button variant="outline" className="w-full bg-transparent">
              <Heart className="h-4 w-4 mr-2" /> Save Property
            </Button>
            <Button variant="outline" className="w-full bg-transparent">
              <Share2 className="h-4 w-4 mr-2" /> Share Listing
            </Button>
            <Button className="w-full">Schedule a Tour</Button>
          </CardContent>
        </Card>

        {/* Recommended Properties */}
        <RecommendedProperties currentPropertyId={property.id} />
      </div>
    </div>
  )
}
