import { Skeleton } from "@/components/ui/skeleton"
import { <PERSON>, CardContent, CardHeader } from "@/components/ui/card"

export default function RentEasyLoading() {
  return (
    <div className="p-4 md:p-8 lg:p-12">
      <div className="text-center mb-10 space-y-3">
        <Skeleton className="h-10 w-64 mx-auto" />
        <Skeleton className="h-6 w-96 mx-auto" />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
        {Array.from({ length: 3 }).map((_, i) => (
          <Card key={i} className="flex flex-col justify-between p-6 shadow-lg">
            <CardHeader className="text-center pb-4 space-y-2">
              <Skeleton className="h-6 w-32 mx-auto" />
              <Skeleton className="h-10 w-40 mx-auto mt-2" />
              <Skeleton className="h-4 w-24 mx-auto" />
            </CardHeader>
            <CardContent className="flex-1 space-y-3">
              {Array.from({ length: 3 }).map((_, j) => (
                <div key={j} className="flex items-center gap-2">
                  <Skeleton className="h-5 w-5 rounded-full" />
                  <Skeleton className="h-4 w-3/4" />
                </div>
              ))}
            </CardContent>
            <div className="mt-6">
              <Skeleton className="h-10 w-full" />
            </div>
          </Card>
        ))}
      </div>

      <div className="mt-16 text-center space-y-4">
        <Skeleton className="h-8 w-64 mx-auto" />
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mt-6">
          {Array.from({ length: 3 }).map((_, i) => (
            <div key={i} className="flex flex-col items-center text-center space-y-3">
              <Skeleton className="h-12 w-12 rounded-full mb-4" />
              <Skeleton className="h-6 w-32" />
              <Skeleton className="h-4 w-full" />
              <Skeleton className="h-4 w-5/6" />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
