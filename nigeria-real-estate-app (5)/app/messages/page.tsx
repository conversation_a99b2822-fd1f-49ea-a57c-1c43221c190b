import { <PERSON>, <PERSON><PERSON>onte<PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { But<PERSON> } from "@/components/ui/button"
import { ScrollArea } from "@/components/ui/scroll-area"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Send } from "lucide-react"

export default function MessagesPage() {
  const messages = [
    {
      id: 1,
      sender: "John Doe",
      avatar: "/placeholder.svg?height=40&width=40",
      content: "Hi, is the property at Lekki still available?",
      time: "10:00 AM",
    },
    {
      id: 2,
      sender: "You",
      avatar: "/placeholder.svg?height=40&width=40",
      content: "Yes, it is. Are you interested in a viewing?",
      time: "10:05 AM",
    },
    {
      id: 3,
      sender: "John Doe",
      avatar: "/placeholder.svg?height=40&width=40",
      content: "Yes, I am. What's the best time this week?",
      time: "10:10 AM",
    },
    {
      id: 4,
      sender: "You",
      avatar: "/placeholder.svg?height=40&width=40",
      content: "How about Wednesday at 2 PM?",
      time: "10:15 AM",
    },
  ]

  return (
    <div className="flex flex-col h-full p-4">
      <Card className="flex-1 flex flex-col">
        <CardHeader>
          <CardTitle>Messages</CardTitle>
        </CardHeader>
        <CardContent className="flex-1 flex flex-col">
          <ScrollArea className="flex-1 p-4 border rounded-md mb-4">
            {messages.map((message) => (
              <div
                key={message.id}
                className={`flex items-start gap-3 mb-4 ${message.sender === "You" ? "justify-end" : ""}`}
              >
                {message.sender !== "You" && (
                  <Avatar>
                    <AvatarImage src={message.avatar || "/placeholder.svg"} alt={message.sender} />
                    <AvatarFallback>{message.sender.charAt(0)}</AvatarFallback>
                  </Avatar>
                )}
                <div className={`flex flex-col ${message.sender === "You" ? "items-end" : "items-start"}`}>
                  <div
                    className={`rounded-lg p-3 max-w-[70%] ${message.sender === "You" ? "bg-primary text-primary-foreground" : "bg-muted"}`}
                  >
                    <p className="text-sm">{message.content}</p>
                  </div>
                  <span className="text-xs text-muted-foreground mt-1">{message.time}</span>
                </div>
                {message.sender === "You" && (
                  <Avatar>
                    <AvatarImage src={message.avatar || "/placeholder.svg"} alt={message.sender} />
                    <AvatarFallback>{message.sender.charAt(0)}</AvatarFallback>
                  </Avatar>
                )}
              </div>
            ))}
          </ScrollArea>
          <div className="flex gap-2">
            <Input placeholder="Type your message..." className="flex-1" />
            <Button size="icon">
              <Send className="h-4 w-4" />
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
