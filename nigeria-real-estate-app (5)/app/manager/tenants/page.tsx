import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { MoreHorizontal, PlusCircle, Edit, Trash2, MessageSquare } from "lucide-react"
import Link from "next/link"

interface Tenant {
  id: string
  firstName: string
  lastName: string
  email: string
  phone: string
  propertyName: string
  leaseEndDate: string
  status: "active" | "inactive" | "pending"
}

async function getManagerTenants(): Promise<Tenant[]> {
  // Simulate fetching data
  return [
    {
      id: "tenant_001",
      firstName: "Aisha",
      lastName: "Bello",
      email: "<EMAIL>",
      phone: "+2348011112222",
      propertyName: "Modern 3-Bedroom Apartment",
      leaseEndDate: "2025-03-31",
      status: "active",
    },
    {
      id: "tenant_002",
      firstName: "<PERSON>",
      lastName: "Okoro",
      email: "<EMAIL>",
      phone: "+2348033334444",
      propertyName: "Spacious 2-Bedroom Flat",
      leaseEndDate: "2024-08-15",
      status: "active",
    },
    {
      id: "tenant_003",
      firstName: "Grace",
      lastName: "Obi",
      email: "<EMAIL>",
      phone: "+2348055556666",
      propertyName: "Studio Apartment, Yaba",
      leaseEndDate: "2024-07-31",
      status: "inactive",
    },
    {
      id: "tenant_004",
      firstName: "David",
      lastName: "Eze",
      email: "<EMAIL>",
      phone: "+2348077778888",
      propertyName: "4-Bedroom House, Abuja",
      leaseEndDate: "2025-01-31",
      status: "active",
    },
  ]
}

export default async function ManagerTenantsPage() {
  const tenants = await getManagerTenants()

  return (
    <div className="p-4 md:p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">Tenant Management</h1>
        <Button asChild>
          <Link href="/manager/tenants/add">
            <PlusCircle className="mr-2 h-4 w-4" /> Add New Tenant
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>All Tenants</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Name</TableHead>
                <TableHead>Email</TableHead>
                <TableHead>Phone</TableHead>
                <TableHead>Property</TableHead>
                <TableHead>Lease End</TableHead>
                <TableHead>Status</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {tenants.map((tenant) => (
                <TableRow key={tenant.id}>
                  <TableCell className="font-medium">
                    {tenant.firstName} {tenant.lastName}
                  </TableCell>
                  <TableCell>{tenant.email}</TableCell>
                  <TableCell>{tenant.phone}</TableCell>
                  <TableCell>{tenant.propertyName}</TableCell>
                  <TableCell>{tenant.leaseEndDate}</TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        tenant.status === "active" ? "default" : tenant.status === "inactive" ? "secondary" : "outline"
                      }
                    >
                      {tenant.status.charAt(0).toUpperCase() + tenant.status.slice(1)}
                    </Badge>
                  </TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem>View Details</DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="mr-2 h-4 w-4" /> Edit Tenant
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <MessageSquare className="mr-2 h-4 w-4" /> Send Message
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-destructive">
                          <Trash2 className="mr-2 h-4 w-4" /> Remove Tenant
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
