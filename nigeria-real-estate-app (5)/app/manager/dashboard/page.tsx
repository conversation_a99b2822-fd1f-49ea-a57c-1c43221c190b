import { Consistent<PERSON>ead<PERSON> } from "@/components/ui/consistent-header"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { DollarSign, Home, Users, MessageSquare, TrendingUp, PlusCircle, Wrench, CalendarCheck } from "lucide-react"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowUpDown, MoreHorizontal } from "lucide-react"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { Badge } from "@/components/ui/badge"
import Link from "next/link"

export default function ManagerDashboardPage() {
  const dashboardOverview = {
    totalPropertiesManaged: 125,
    occupancyRate: "94.5%",
    totalRevenueManaged: "₦1.2 Billion",
    newInquiries: 23,
    overduePayments: 5,
    expiringLeases: 7,
    pendingMaintenance: 3,
  }

  const recentActivities = [
    {
      id: "act1",
      type: "New Inquiry",
      description: "Interest in Luxury 4-Bedroom Villa",
      date: "2024-07-10",
      status: "New",
    },
    {
      id: "act2",
      type: "Payment Received",
      description: "Rent from Alice Johnson (Apartment 3B)",
      date: "2024-07-01",
      status: "Completed",
    },
    {
      id: "act3",
      type: "Maintenance Request",
      description: "Leaky faucet at Duplex 5",
      date: "2024-06-28",
      status: "Pending",
    },
    {
      id: "act4",
      type: "Property Added",
      description: "New listing: Commercial Office Space",
      date: "2024-06-25",
      status: "Completed",
    },
  ]

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "New":
        return <Badge className="bg-blue-500 hover:bg-blue-600 text-white">New</Badge>
      case "Completed":
        return <Badge className="bg-green-500 hover:bg-green-600 text-white">Completed</Badge>
      case "Pending":
        return <Badge variant="outline">Pending</Badge>
      default:
        return <Badge variant="secondary">{status}</Badge>
    }
  }

  return (
    <div className="flex h-full flex-col">
      <ConsistentHeader title="Manager Dashboard" />
      <div className="p-4 md:p-6 lg:p-8">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Properties Managed</CardTitle>
              <Home className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dashboardOverview.totalPropertiesManaged}</div>
              <p className="text-xs text-gray-500">Total properties in portfolio</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Occupancy Rate</CardTitle>
              <Users className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dashboardOverview.occupancyRate}</div>
              <p className="text-xs text-gray-500">Current portfolio occupancy</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">Total Revenue</CardTitle>
              <DollarSign className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dashboardOverview.totalRevenueManaged}</div>
              <p className="text-xs text-gray-500">Managed revenue YTD</p>
            </CardContent>
          </Card>
          <Card>
            <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
              <CardTitle className="text-sm font-medium">New Inquiries</CardTitle>
              <MessageSquare className="h-4 w-4 text-gray-500" />
            </CardHeader>
            <CardContent>
              <div className="text-2xl font-bold">{dashboardOverview.newInquiries}</div>
              <p className="text-xs text-gray-500">Last 7 days</p>
            </CardContent>
          </Card>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-6 mb-8">
          <Card>
            <CardHeader>
              <CardTitle>Quick Actions</CardTitle>
            </CardHeader>
            <CardContent className="grid grid-cols-1 sm:grid-cols-2 gap-4">
              <Button asChild>
                <Link href="/manager/add-property">
                  <PlusCircle className="h-4 w-4 mr-2" /> Add New Property
                </Link>
              </Button>
              <Button>
                <Users className="h-4 w-4 mr-2" /> Add New Tenant
              </Button>
              <Button>
                <DollarSign className="h-4 w-4 mr-2" /> Record Payment
              </Button>
              <Button variant="outline">
                <TrendingUp className="h-4 w-4 mr-2" /> Boost Listing
              </Button>
            </CardContent>
          </Card>

          <Card>
            <CardHeader>
              <CardTitle>Alerts & Reminders</CardTitle>
            </CardHeader>
            <CardContent className="space-y-3">
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <DollarSign className="h-5 w-5 text-red-500" />
                  <span className="font-medium">Overdue Payments</span>
                </div>
                <Badge variant="destructive">{dashboardOverview.overduePayments}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <CalendarCheck className="h-5 w-5 text-yellow-500" />
                  <span className="font-medium">Expiring Leases</span>
                </div>
                <Badge variant="secondary">{dashboardOverview.expiringLeases}</Badge>
              </div>
              <div className="flex items-center justify-between">
                <div className="flex items-center space-x-2">
                  <Wrench className="h-5 w-5 text-orange-500" />
                  <span className="font-medium">Pending Maintenance</span>
                </div>
                <Badge variant="secondary">{dashboardOverview.pendingMaintenance}</Badge>
              </div>
            </CardContent>
          </Card>
        </div>

        <Card>
          <CardHeader>
            <CardTitle>Recent Activity</CardTitle>
          </CardHeader>
          <CardContent>
            <Table>
              <TableHeader>
                <TableRow>
                  <TableHead>Type</TableHead>
                  <TableHead>Description</TableHead>
                  <TableHead>
                    <Button variant="ghost">
                      Date <ArrowUpDown className="ml-2 h-4 w-4" />
                    </Button>
                  </TableHead>
                  <TableHead>Status</TableHead>
                  <TableHead className="text-right">Actions</TableHead>
                </TableRow>
              </TableHeader>
              <TableBody>
                {recentActivities.map((activity) => (
                  <TableRow key={activity.id}>
                    <TableCell className="font-medium">{activity.type}</TableCell>
                    <TableCell>{activity.description}</TableCell>
                    <TableCell>{activity.date}</TableCell>
                    <TableCell>{getStatusBadge(activity.status)}</TableCell>
                    <TableCell className="text-right">
                      <DropdownMenu>
                        <DropdownMenuTrigger asChild>
                          <Button variant="ghost" className="h-8 w-8 p-0">
                            <span className="sr-only">Open menu</span>
                            <MoreHorizontal className="h-4 w-4" />
                          </Button>
                        </DropdownMenuTrigger>
                        <DropdownMenuContent align="end">
                          <DropdownMenuItem>View Details</DropdownMenuItem>
                          <DropdownMenuItem>Mark as Read</DropdownMenuItem>
                        </DropdownMenuContent>
                      </DropdownMenu>
                    </TableCell>
                  </TableRow>
                ))}
              </TableBody>
            </Table>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
