"use client"

import type React from "react"

import { useState } from "react"
import { Card, CardContent, CardHeader, CardTitle, CardDescription } from "@/components/ui/card"
import { Label } from "@/components/ui/label"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { Calendar } from "@/components/ui/calendar"
import { Popover, PopoverContent, PopoverTrigger } from "@/components/ui/popover"
import { CalendarIcon, Rocket } from "lucide-react"
import { format } from "date-fns"
import { cn } from "@/lib/utils"
import { useToast } from "@/hooks/use-toast"

interface PropertyDetails {
  id: string
  title: string
  location: string
  currentBoostStatus?: string
}

async function getPropertyDetails(id: string): Promise<PropertyDetails> {
  // Simulate fetching property details
  return {
    id,
    title: "Luxury 4-Bedroom Duplex",
    location: "Ikoyi, Lagos",
    currentBoostStatus: "None", // or "Active (ends 2024-08-15)"
  }
}

export default function BoostPropertyPage({ params }: { params: { id: string } }) {
  const { toast } = useToast()
  const [property, setProperty] = useState<PropertyDetails | null>(null)
  const [boostPlan, setBoostPlan] = useState("standard")
  const [duration, setDuration] = useState<Date | undefined>(undefined)
  const [budget, setBudget] = useState("")
  const [targetAudience, setTargetAudience] = useState<string[]>([])

  useState(() => {
    getPropertyDetails(params.id).then(setProperty)
  })

  const handleBoostSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    console.log("Boosting property:", {
      propertyId: params.id,
      boostPlan,
      duration,
      budget,
      targetAudience,
    })
    toast({
      title: "Property Boosted!",
      description: `"${property?.title}" is now boosted with the ${boostPlan} plan.`,
    })
    // In a real app, you'd send this to your backend
  }

  const boostPlans = [
    { value: "basic", label: "Basic Boost (7 days)", price: "₦50,000" },
    { value: "standard", label: "Standard Boost (14 days)", price: "₦120,000" },
    { value: "premium", label: "Premium Boost (30 days)", price: "₦250,000" },
  ]

  const audienceOptions = ["Families", "Young Professionals", "Investors", "Expats", "Students"]

  if (!property) {
    return <div className="p-4 md:p-6 lg:p-8 text-center">Loading property details...</div>
  }

  return (
    <div className="p-4 md:p-6 lg:p-8">
      <Card className="max-w-3xl mx-auto">
        <CardHeader>
          <CardTitle className="text-2xl font-bold flex items-center gap-2">
            <Rocket className="h-6 w-6" /> Boost Property: {property.title}
          </CardTitle>
          <CardDescription>
            Increase visibility and reach more potential buyers/tenants for this property.
          </CardDescription>
        </CardHeader>
        <CardContent>
          <form onSubmit={handleBoostSubmit} className="space-y-6">
            <div className="space-y-2">
              <Label>Property Details</Label>
              <Input value={`ID: ${property.id}`} readOnly />
              <Input value={`Location: ${property.location}`} readOnly />
              <Input value={`Current Boost Status: ${property.currentBoostStatus}`} readOnly />
            </div>

            <div className="space-y-2">
              <Label htmlFor="boostPlan">Choose Boost Plan</Label>
              <Select value={boostPlan} onValueChange={setBoostPlan}>
                <SelectTrigger id="boostPlan">
                  <SelectValue placeholder="Select a boost plan" />
                </SelectTrigger>
                <SelectContent>
                  {boostPlans.map((plan) => (
                    <SelectItem key={plan.value} value={plan.value}>
                      {plan.label} - {plan.price}
                    </SelectItem>
                  ))}
                </SelectContent>
              </Select>
            </div>

            <div className="space-y-2">
              <Label htmlFor="duration">Boost End Date</Label>
              <Popover>
                <PopoverTrigger asChild>
                  <Button
                    variant={"outline"}
                    className={cn("w-full justify-start text-left font-normal", !duration && "text-muted-foreground")}
                  >
                    <CalendarIcon className="mr-2 h-4 w-4" />
                    {duration ? format(duration, "PPP") : <span>Pick a date</span>}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-auto p-0">
                  <Calendar mode="single" selected={duration} onSelect={setDuration} initialFocus />
                </PopoverContent>
              </Popover>
            </div>

            <div className="space-y-2">
              <Label htmlFor="budget">Additional Budget (Optional, ₦)</Label>
              <Input
                id="budget"
                type="number"
                placeholder="e.g., 100000"
                value={budget}
                onChange={(e) => setBudget(e.target.value)}
              />
              <CardDescription>Allocate extra funds for broader reach.</CardDescription>
            </div>

            <div className="space-y-2">
              <Label>Target Audience (Optional)</Label>
              <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                {audienceOptions.map((audience) => (
                  <div key={audience} className="flex items-center space-x-2">
                    <Checkbox
                      id={audience}
                      checked={targetAudience.includes(audience)}
                      onCheckedChange={(checked) => {
                        setTargetAudience((prev) =>
                          checked ? [...prev, audience] : prev.filter((a) => a !== audience),
                        )
                      }}
                    />
                    <Label htmlFor={audience}>{audience}</Label>
                  </div>
                ))}
              </div>
              <CardDescription>Select specific groups to target your boost.</CardDescription>
            </div>

            <Button type="submit" className="w-full">
              Confirm Boost
            </Button>
          </form>
        </CardContent>
      </Card>
    </div>
  )
}
