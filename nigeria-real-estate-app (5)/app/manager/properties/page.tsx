import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/card"
import { Table, TableBody, TableCell, TableHead, TableHeader, TableRow } from "@/components/ui/table"
import { But<PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { DropdownMenu, DropdownMenuContent, DropdownMenuItem, DropdownMenuTrigger } from "@/components/ui/dropdown-menu"
import { MoreHorizontal, PlusCircle, Edit, Trash2, Rocket } from "lucide-react"
import Link from "next/link"

interface Property {
  id: string
  title: string
  location: string
  price: string
  purpose: "rent" | "sale"
  status: "available" | "occupied" | "maintenance" | "vacant"
  bedrooms: number
  bathrooms: number
}

async function getManagerProperties(): Promise<Property[]> {
  // Simulate fetching data
  return [
    {
      id: "prop_001",
      title: "Luxury 4-Bedroom Duplex",
      location: "Ikoyi, Lagos",
      price: "₦150,000,000",
      purpose: "sale",
      status: "available",
      bedrooms: 4,
      bathrooms: 5,
    },
    {
      id: "prop_002",
      title: "Modern 3-Bedroom Apartment",
      location: "Victoria Island, Lagos",
      price: "₦2,500,000/year",
      purpose: "rent",
      status: "occupied",
      bedrooms: 3,
      bathrooms: 2,
    },
    {
      id: "prop_003",
      title: "Spacious 2-Bedroom Flat",
      location: "Yaba, Lagos",
      price: "₦1,200,000/year",
      purpose: "rent",
      status: "maintenance",
      bedrooms: 2,
      bathrooms: 2,
    },
    {
      id: "prop_004",
      title: "Commercial Land",
      location: "Lekki, Lagos",
      price: "₦90,000,000",
      purpose: "sale",
      status: "available",
      bedrooms: 0,
      bathrooms: 0,
    },
    {
      id: "prop_005",
      title: "5-Bedroom Villa",
      location: "Banana Island, Lagos",
      price: "₦450,000,000",
      purpose: "sale",
      status: "available",
      bedrooms: 5,
      bathrooms: 6,
    },
  ]
}

export default async function ManagerPropertiesPage() {
  const properties = await getManagerProperties()

  return (
    <div className="p-4 md:p-6 space-y-6">
      <div className="flex items-center justify-between">
        <h1 className="text-3xl font-bold">My Properties</h1>
        <Button asChild>
          <Link href="/manager/properties/add">
            <PlusCircle className="mr-2 h-4 w-4" /> Add New Property
          </Link>
        </Button>
      </div>

      <Card>
        <CardHeader>
          <CardTitle>Property Portfolio</CardTitle>
        </CardHeader>
        <CardContent>
          <Table>
            <TableHeader>
              <TableRow>
                <TableHead>Title</TableHead>
                <TableHead>Location</TableHead>
                <TableHead>Price</TableHead>
                <TableHead>Purpose</TableHead>
                <TableHead>Status</TableHead>
                <TableHead>Beds</TableHead>
                <TableHead>Baths</TableHead>
                <TableHead className="text-right">Actions</TableHead>
              </TableRow>
            </TableHeader>
            <TableBody>
              {properties.map((property) => (
                <TableRow key={property.id}>
                  <TableCell className="font-medium">{property.title}</TableCell>
                  <TableCell>{property.location}</TableCell>
                  <TableCell>{property.price}</TableCell>
                  <TableCell>{property.purpose.charAt(0).toUpperCase() + property.purpose.slice(1)}</TableCell>
                  <TableCell>
                    <Badge
                      variant={
                        property.status === "available"
                          ? "default"
                          : property.status === "occupied"
                            ? "secondary"
                            : "destructive"
                      }
                    >
                      {property.status.charAt(0).toUpperCase() + property.status.slice(1)}
                    </Badge>
                  </TableCell>
                  <TableCell>{property.bedrooms}</TableCell>
                  <TableCell>{property.bathrooms}</TableCell>
                  <TableCell className="text-right">
                    <DropdownMenu>
                      <DropdownMenuTrigger asChild>
                        <Button variant="ghost" className="h-8 w-8 p-0">
                          <span className="sr-only">Open menu</span>
                          <MoreHorizontal className="h-4 w-4" />
                        </Button>
                      </DropdownMenuTrigger>
                      <DropdownMenuContent align="end">
                        <DropdownMenuItem asChild>
                          <Link href={`/property/${property.id}`}>View Details</Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem>
                          <Edit className="mr-2 h-4 w-4" /> Edit Property
                        </DropdownMenuItem>
                        <DropdownMenuItem asChild>
                          <Link href={`/manager/boost-property/${property.id}`}>
                            <Rocket className="mr-2 h-4 w-4" /> Boost Listing
                          </Link>
                        </DropdownMenuItem>
                        <DropdownMenuItem className="text-destructive">
                          <Trash2 className="mr-2 h-4 w-4" /> Delete Property
                        </DropdownMenuItem>
                      </DropdownMenuContent>
                    </DropdownMenu>
                  </TableCell>
                </TableRow>
              ))}
            </TableBody>
          </Table>
        </CardContent>
      </Card>
    </div>
  )
}
