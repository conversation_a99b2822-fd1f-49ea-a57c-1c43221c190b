"use client"

import type React from "react"
import { useState } from "react"
import { useRouter } from "next/navigation"
import {
  ArrowLeft,
  ArrowRight,
  Upload,
  X,
  MapPin,
  User,
  Home,
  FileText,
  Camera,
  PlusCircle,
  UploadCloud,
} from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Textarea } from "@/components/ui/textarea"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Progress } from "@/components/ui/progress"
import { Separator } from "@/components/ui/separator"
import Image from "next/image"
import { ConsistentHeader } from "@/components/ui/consistent-header"

const steps = [
  { id: 1, title: "Property Details", icon: Home },
  { id: 2, title: "Owner Information", icon: User },
  { id: 3, title: "Photos & Documents", icon: FileText },
  { id: 4, title: "Additional Info", icon: MapPin },
]

const amenitiesList = [
  "Parking Space",
  "Security",
  "Generator",
  "Water Supply",
  "Internet",
  "Air Conditioning",
  "Garden",
  "Swimming Pool",
  "Gym",
  "Elevator",
  "Balcony",
  "Furnished",
]

const featuresList = [
  "Furnished",
  "Parking",
  "Security",
  "Generator",
  "Swimming Pool",
  "Garden",
  "Balcony",
  "Waterfront",
]

function AddPropertyPage() {
  const router = useRouter()
  const [currentStep, setCurrentStep] = useState(1)
  const [propertyType, setPropertyType] = useState("")
  const [purpose, setPurpose] = useState("")
  const [features, setFeatures] = useState<string[]>([])
  const [amenities, setAmenities] = useState<string[]>([])
  const [images, setImages] = useState<File[]>([])
  const [selectedAmenities, setSelectedAmenities] = useState<string[]>([])
  const [uploadedPhotos, setUploadedPhotos] = useState<string[]>([])
  const [uploadedDocuments, setUploadedDocuments] = useState<string[]>([])

  const progress = (currentStep / steps.length) * 100

  const nextStep = () => {
    if (currentStep < steps.length) {
      setCurrentStep(currentStep + 1)
    }
  }

  const prevStep = () => {
    if (currentStep > 1) {
      setCurrentStep(currentStep - 1)
    }
  }

  const toggleAmenity = (amenity: string) => {
    setSelectedAmenities((prev) => (prev.includes(amenity) ? prev.filter((a) => a !== amenity) : [...prev, amenity]))
  }

  const handleFeatureChange = (feature: string) => {
    setFeatures((prev) => (prev.includes(feature) ? prev.filter((f) => f !== feature) : [...prev, feature]))
  }

  const handleAmenityChange = (amenity: string) => {
    setAmenities((prev) => (prev.includes(amenity) ? prev.filter((a) => a !== amenity) : [...prev, amenity]))
  }

  const handleImageUpload = (event: React.ChangeEvent<HTMLInputElement>) => {
    if (event.target.files) {
      setImages((prev) => [...prev, ...Array.from(event.target.files)])
    }
  }

  const handleSubmit = (event: React.FormEvent) => {
    event.preventDefault()
    // Handle form submission logic here
    console.log({
      propertyType,
      purpose,
      features,
      amenities,
      images,
      // ... other form fields
    })
    alert("Property added successfully (simulated)!")
    router.push("/manager/properties")
  }

  return (
    <div className="min-h-screen bg-gray-50">
      {/* Header */}
      <ConsistentHeader title="Add New Property" />

      {/* Progress */}
      <div className="px-4 py-3 bg-white border-b">
        <Progress value={progress} className="h-2" />
        <div className="flex justify-between mt-2 text-xs text-gray-600">
          {steps.map((step) => (
            <span key={step.id} className={currentStep >= step.id ? "text-green-600 font-medium" : ""}>
              {step.title}
            </span>
          ))}
        </div>
      </div>

      <div className="p-4">
        {/* Step 1: Property Details */}
        {currentStep === 1 && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Home className="h-5 w-5" />
                  Property Details
                </CardTitle>
                <CardDescription>Fill in the details for your new property listing.</CardDescription>
              </CardHeader>
              <CardContent className="space-y-4">
                <form onSubmit={handleSubmit} className="space-y-6">
                  {/* Basic Information */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="property-title">Property Title</Label>
                      <Input id="property-title" placeholder="e.g. Modern 3-Bedroom Apartment" required />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="address">Full Address</Label>
                      <Input id="address" placeholder="Enter complete property address" required />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="property-type">Property Type</Label>
                      <Select value={propertyType} onValueChange={setPropertyType} required>
                        <SelectTrigger id="property-type">
                          <SelectValue placeholder="Select type" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="apartment">Apartment</SelectItem>
                          <SelectItem value="duplex">Duplex</SelectItem>
                          <SelectItem value="house">House</SelectItem>
                          <SelectItem value="studio">Studio</SelectItem>
                          <SelectItem value="bungalow">Bungalow</SelectItem>
                          <SelectItem value="mansion">Mansion</SelectItem>
                          <SelectItem value="villa">Villa</SelectItem>
                          <SelectItem value="land">Land</SelectItem>
                          <SelectItem value="commercial">Commercial</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="purpose">Purpose</Label>
                      <Select value={purpose} onValueChange={setPurpose} required>
                        <SelectTrigger id="purpose">
                          <SelectValue placeholder="For Rent or Sale" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="rent">For Rent</SelectItem>
                          <SelectItem value="sale">For Sale</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="price">Price (₦)</Label>
                      <Input id="price" type="number" placeholder="e.g. 2500000" required />
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="size">Size (sqm)</Label>
                      <Input id="size" type="number" placeholder="e.g. 120" />
                    </div>
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="description">Description</Label>
                    <Textarea
                      id="description"
                      placeholder="Provide a detailed description of the property..."
                      rows={5}
                      required
                    />
                  </div>

                  {/* Specifications */}
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <div className="space-y-2">
                      <Label htmlFor="bedrooms">Bedrooms</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                        <SelectContent>
                          {[1, 2, 3, 4, 5, 6].map((num) => (
                            <SelectItem key={num} value={num.toString()}>
                              {num}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                    <div className="space-y-2">
                      <Label htmlFor="bathrooms">Bathrooms</Label>
                      <Select>
                        <SelectTrigger>
                          <SelectValue placeholder="Select" />
                        </SelectTrigger>
                        <SelectContent>
                          {[1, 2, 3, 4, 5].map((num) => (
                            <SelectItem key={num} value={num.toString()}>
                              {num}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>

                  {/* Features */}
                  <div className="space-y-2">
                    <Label>Features</Label>
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                      {featuresList.map((feature) => (
                        <div key={feature} className="flex items-center space-x-2">
                          <Checkbox
                            id={`feature-${feature}`}
                            checked={features.includes(feature)}
                            onCheckedChange={() => handleFeatureChange(feature)}
                          />
                          <Label htmlFor={`feature-${feature}`}>{feature}</Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Amenities */}
                  <div className="space-y-2">
                    <Label>Amenities</Label>
                    <div className="grid grid-cols-2 sm:grid-cols-3 gap-2">
                      {amenitiesList.map((amenity) => (
                        <div key={amenity} className="flex items-center space-x-2">
                          <Checkbox
                            id={`amenity-${amenity}`}
                            checked={amenities.includes(amenity)}
                            onCheckedChange={() => handleAmenityChange(amenity)}
                          />
                          <Label htmlFor={`amenity-${amenity}`}>{amenity}</Label>
                        </div>
                      ))}
                    </div>
                  </div>

                  {/* Images Upload */}
                  <div className="space-y-2">
                    <Label htmlFor="images">Property Images</Label>
                    <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center cursor-pointer hover:border-gray-400">
                      <input
                        id="images"
                        type="file"
                        multiple
                        accept="image/*"
                        className="hidden"
                        onChange={handleImageUpload}
                      />
                      <label htmlFor="images" className="flex flex-col items-center justify-center space-y-2">
                        <UploadCloud className="h-8 w-8 text-gray-400" />
                        <p className="text-gray-600">Drag & drop images here, or click to browse</p>
                        <p className="text-sm text-gray-500">Supports JPG, PNG, WEBP (Max 10 images)</p>
                      </label>
                    </div>
                    {images.length > 0 && (
                      <div className="mt-4 grid grid-cols-3 gap-2">
                        {images.map((file, index) => (
                          <div key={index} className="relative h-24 w-full rounded-md overflow-hidden">
                            <img
                              src={URL.createObjectURL(file) || "/placeholder.svg"}
                              alt={`Uploaded image ${index + 1}`}
                              className="h-full w-full object-cover"
                            />
                            <span className="absolute bottom-1 left-1 text-xs text-white bg-black/50 px-1 rounded">
                              {file.name}
                            </span>
                          </div>
                        ))}
                      </div>
                    )}
                  </div>

                  <Button type="submit" className="w-full">
                    <PlusCircle className="h-4 w-4 mr-2" /> Add Property
                  </Button>
                </form>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Step 2: Owner Information */}
        {currentStep === 2 && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <User className="h-5 w-5" />
                  Property Owner Information
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-4">
                  <div className="space-y-2">
                    <Label htmlFor="owner-first-name">First Name</Label>
                    <Input id="owner-first-name" placeholder="Enter first name" />
                  </div>

                  <div className="space-y-2">
                    <Label htmlFor="owner-last-name">Last Name</Label>
                    <Input id="owner-last-name" placeholder="Enter last name" />
                  </div>
                </div>

                <div className="space-y-2">
                  <Label htmlFor="owner-email">Email Address</Label>
                  <Input id="owner-email" type="email" placeholder="<EMAIL>" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="owner-phone">Phone Number</Label>
                  <Input id="owner-phone" placeholder="+234 xxx xxx xxxx" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="owner-address">Owner's Address</Label>
                  <Textarea id="owner-address" placeholder="Enter owner's residential address" />
                </div>

                <Separator />

                <div className="space-y-2">
                  <Label htmlFor="emergency-contact">Emergency Contact Name</Label>
                  <Input id="emergency-contact" placeholder="Enter emergency contact name" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="emergency-phone">Emergency Contact Phone</Label>
                  <Input id="emergency-phone" placeholder="+234 xxx xxx xxxx" />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="relationship">Relationship to Owner</Label>
                  <Select>
                    <SelectTrigger>
                      <SelectValue placeholder="Select relationship" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="spouse">Spouse</SelectItem>
                      <SelectItem value="child">Child</SelectItem>
                      <SelectItem value="sibling">Sibling</SelectItem>
                      <SelectItem value="parent">Parent</SelectItem>
                      <SelectItem value="friend">Friend</SelectItem>
                      <SelectItem value="business-partner">Business Partner</SelectItem>
                      <SelectItem value="other">Other</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Step 3: Photos & Documents */}
        {currentStep === 3 && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Camera className="h-5 w-5" />
                  Property Photos
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <Camera className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm font-medium mb-1">Upload Property Photos</p>
                  <p className="text-xs text-gray-500 mb-4">Add up to 10 high-quality photos</p>
                  <Button variant="outline">
                    <Upload className="h-4 w-4 mr-2" />
                    Choose Files
                  </Button>
                </div>

                {uploadedPhotos.length > 0 && (
                  <div className="grid grid-cols-3 gap-3">
                    {uploadedPhotos.map((photo, index) => (
                      <div key={index} className="relative">
                        <Image
                          src={photo || "/placeholder.svg"}
                          alt={`Property photo ${index + 1}`}
                          width={100}
                          height={100}
                          className="w-full h-24 object-cover rounded-md"
                        />
                        <Button
                          variant="destructive"
                          size="sm"
                          className="absolute -top-2 -right-2 h-6 w-6 p-0 rounded-full"
                        >
                          <X className="h-3 w-3" />
                        </Button>
                      </div>
                    ))}
                  </div>
                )}
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <FileText className="h-5 w-5" />
                  Property Documents
                </CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="border-2 border-dashed border-gray-300 rounded-lg p-6 text-center">
                  <FileText className="h-8 w-8 text-gray-400 mx-auto mb-2" />
                  <p className="text-sm font-medium mb-1">Upload Documents</p>
                  <p className="text-xs text-gray-500 mb-4">Property title, permits, certificates, etc.</p>
                  <Button variant="outline">
                    <Upload className="h-4 w-4 mr-2" />
                    Choose Files
                  </Button>
                </div>

                <div className="space-y-2">
                  <Label>Document Types (Check all that apply)</Label>
                  <div className="grid grid-cols-2 gap-2">
                    {[
                      "Property Title/Deed",
                      "Building Permit",
                      "Certificate of Occupancy",
                      "Survey Plan",
                      "Tax Clearance",
                      "Insurance Policy",
                      "Utility Bills",
                      "Other Documents",
                    ].map((doc) => (
                      <div key={doc} className="flex items-center space-x-2">
                        <Checkbox id={doc} />
                        <Label htmlFor={doc} className="text-sm">
                          {doc}
                        </Label>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Step 4: Additional Information */}
        {currentStep === 4 && (
          <div className="space-y-6">
            <Card>
              <CardHeader>
                <CardTitle>Property Status</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-3">
                  <Label>Current Status</Label>
                  <RadioGroup defaultValue="vacant">
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="vacant" id="vacant" />
                      <Label htmlFor="vacant">Vacant - Ready for new tenant</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="occupied" id="occupied" />
                      <Label htmlFor="occupied">Occupied - Has existing tenant</Label>
                    </div>
                    <div className="flex items-center space-x-2">
                      <RadioGroupItem value="maintenance" id="maintenance" />
                      <Label htmlFor="maintenance">Under Maintenance/Renovation</Label>
                    </div>
                  </RadioGroup>
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Amenities & Features</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="grid grid-cols-2 gap-3">
                  {selectedAmenities.map((amenity) => (
                    <div key={amenity} className="flex items-center space-x-2">
                      <Checkbox
                        id={amenity}
                        checked={selectedAmenities.includes(amenity)}
                        onCheckedChange={() => toggleAmenity(amenity)}
                      />
                      <Label htmlFor={amenity} className="text-sm">
                        {amenity}
                      </Label>
                    </div>
                  ))}
                </div>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Additional Notes</CardTitle>
              </CardHeader>
              <CardContent className="space-y-4">
                <div className="space-y-2">
                  <Label htmlFor="property-notes">Property Notes</Label>
                  <Textarea
                    id="property-notes"
                    placeholder="Add any additional information about the property, special instructions, or important details..."
                    className="min-h-[100px]"
                  />
                </div>

                <div className="space-y-2">
                  <Label htmlFor="management-notes">Management Notes (Private)</Label>
                  <Textarea
                    id="management-notes"
                    placeholder="Private notes for property management purposes only..."
                    className="min-h-[80px]"
                  />
                </div>
              </CardContent>
            </Card>
          </div>
        )}

        {/* Navigation Buttons */}
        <div className="flex gap-3 mt-8">
          {currentStep > 1 && (
            <Button variant="outline" onClick={prevStep} className="flex-1 bg-transparent">
              <ArrowLeft className="h-4 w-4 mr-2" />
              Previous
            </Button>
          )}

          {currentStep < steps.length ? (
            <Button onClick={nextStep} className="flex-1 bg-green-600 hover:bg-green-700">
              Next
              <ArrowRight className="h-4 w-4 ml-2" />
            </Button>
          ) : (
            <Button onClick={handleSubmit} className="flex-1 bg-green-600 hover:bg-green-700">
              Add Property
            </Button>
          )}
        </div>
      </div>
    </div>
  )
}

export default function ManagerAddPropertyPage() {
  return <AddPropertyPage />
}
