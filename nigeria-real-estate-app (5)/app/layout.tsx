import type React from "react"
import type { <PERSON>ada<PERSON> } from "next"
import { Inter } from "next/font/google"
import "./globals.css"
import { ThemeProvider } from "@/components/theme-provider"
import { Toaster } from "@/components/ui/toaster" // Assuming you have a Toaster component
import {
  SidebarProvider,
  Sidebar,
  SidebarContent,
  SidebarMenu,
  SidebarMenuItem,
  SidebarMenuButton,
  SidebarHeader,
  SidebarFooter,
} from "@/components/ui/sidebar"
import {
  Home,
  Search,
  MessageSquare,
  Bookmark,
  User,
  Building,
  DollarSign,
  Settings,
  LayoutDashboard,
  Users,
  BarChart,
  BellRing,
  LogOut,
} from "lucide-react"
import Link from "next/link"
import { AuthModalProvider } from "@/context/auth-modal-context"
import { AuthModal } from "@/components/auth-modal"
import { SessionProvider } from "next-auth/react"
import { auth, signOut } from "@/lib/auth" // Import auth and signOut from the new file

const inter = Inter({ subsets: ["latin"] })

export const metadata: Metadata = {
  title: "Nigeria Real Estate",
  description: "A comprehensive real estate management platform for Nigeria.",
    generator: 'v0.dev'
}

export default async function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  const session = await auth() // Fetch session on the server

  // Define sidebar items based on roles
  const commonItems = [
    { title: "Home", href: "/", icon: Home },
    { title: "Properties", href: "/properties", icon: Building },
    { title: "Search", href: "/search", icon: Search },
  ]

  const authenticatedItems = [
    { title: "Dashboard", href: "/dashboard", icon: LayoutDashboard },
    { title: "Messages", href: "/messages", icon: MessageSquare },
    { title: "Saved", href: "/saved", icon: Bookmark },
    { title: "Profile", href: "/profile", icon: User },
  ]

  const tenantItems = [
    { title: "RentEasy", href: "/renteasy", icon: DollarSign },
    { title: "My Plans", href: "/renteasy/my-plans", icon: BellRing },
  ]

  const landlordItems = [
    { title: "Landlord Dashboard", href: "/landlord/dashboard", icon: LayoutDashboard },
    // Add more landlord specific items
  ]

  const managerItems = [
    { title: "Manager Dashboard", href: "/manager/dashboard", icon: LayoutDashboard },
    { title: "My Properties", href: "/manager/properties", icon: Building },
    { title: "Tenants", href: "/manager/tenants", icon: Users },
    { title: "Payments", href: "/manager/payments", icon: DollarSign },
    { title: "Sales", href: "/manager/sales-dashboard", icon: BarChart },
  ]

  const adminItems = [
    { title: "Admin RentEasy", href: "/admin/renteasy", icon: Settings },
    { title: "Payment Reminders", href: "/admin/payment-reminders", icon: BellRing },
    // Add more admin specific items
  ]

  const getSidebarItems = (role?: string) => {
    let items = [...commonItems]
    if (session?.user) {
      items = [...items, ...authenticatedItems]
      if (role === "tenant") {
        items = [...items, ...tenantItems]
      } else if (role === "landlord") {
        items = [...items, ...landlordItems]
      } else if (role === "manager") {
        items = [...items, ...managerItems]
      } else if (role === "admin") {
        items = [...items, ...adminItems]
      }
    }
    return items
  }

  const sidebarItems = getSidebarItems(session?.user?.role)

  return (
    <html lang="en" suppressHydrationWarning>
      <body className={inter.className}>
        <SessionProvider session={session}>
          <ThemeProvider attribute="class" defaultTheme="system" enableSystem disableTransitionOnChange>
            <AuthModalProvider>
              <SidebarProvider defaultOpen={true}>
                <Sidebar>
                  <SidebarHeader>
                    <Link href="/" className="flex items-center gap-2 font-semibold">
                      <Building className="h-6 w-6" />
                      <span>RealEstate NG</span>
                    </Link>
                  </SidebarHeader>
                  <SidebarContent>
                    <SidebarMenu>
                      {sidebarItems.map((item) => (
                        <SidebarMenuItem key={item.title}>
                          <SidebarMenuButton asChild>
                            <Link href={item.href}>
                              <item.icon className="h-5 w-5" />
                              <span>{item.title}</span>
                            </Link>
                          </SidebarMenuButton>
                        </SidebarMenuItem>
                      ))}
                    </SidebarMenu>
                  </SidebarContent>
                  <SidebarFooter>
                    <SidebarMenu>
                      <SidebarMenuItem>
                        <SidebarMenuButton asChild>
                          <Link href="/settings">
                            <Settings className="h-5 w-5" />
                            <span>Settings</span>
                          </Link>
                        </SidebarMenuButton>
                      </SidebarMenuItem>
                      {session?.user && (
                        <SidebarMenuItem>
                          <form
                            action={async () => {
                              "use server"
                              await signOut()
                            }}
                            className="w-full"
                          >
                            <SidebarMenuButton type="submit" className="w-full">
                              <LogOut className="h-5 w-5" />
                              <span>Logout</span>
                            </SidebarMenuButton>
                          </form>
                        </SidebarMenuItem>
                      )}
                    </SidebarMenu>
                  </SidebarFooter>
                </Sidebar>
                <main className="flex flex-col flex-1 overflow-hidden">{children}</main>
              </SidebarProvider>
              <AuthModal />
            </AuthModalProvider>
            <Toaster />
          </ThemeProvider>
        </SessionProvider>
      </body>
    </html>
  )
}
