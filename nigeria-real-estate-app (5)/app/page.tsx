import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Search, Home, DollarSign, MapPin } from "lucide-react"
import Link from "next/link"
import { PropertyCard } from "@/components/property-card"
import { RecommendedProperties } from "@/components/recommended-properties"
import { RecentSearches } from "@/components/recent-searches"

interface Property {
  id: string
  title: string
  location: string
  price: string
  priceType: string
  purpose: "rent" | "sale"
  type: string
  bedrooms: number
  bathrooms: number
  imageUrl: string
  coordinates: { lat: number; lng: number }
}

async function getFeaturedProperties(): Promise<Property[]> {
  // Simulate fetching featured properties
  return [
    {
      id: "feat_1",
      title: "Luxury 5-Bedroom Villa",
      location: "Banana Island, Lagos",
      price: "₦450,000,000",
      priceType: "sale",
      purpose: "sale",
      type: "Villa",
      bedrooms: 5,
      bathrooms: 6,
      imageUrl: "/placeholder.svg?height=200&width=300",
      coordinates: { lat: 6.4314, lng: 3.4406 },
    },
    {
      id: "feat_2",
      title: "Modern 3-Bedroom Apartment",
      location: "Victoria Island, Lagos",
      price: "₦2,500,000",
      priceType: "year",
      purpose: "rent",
      type: "Apartment",
      bedrooms: 3,
      bathrooms: 2,
      imageUrl: "/placeholder.svg?height=200&width=300",
      coordinates: { lat: 6.4281, lng: 3.4219 },
    },
    {
      id: "feat_3",
      title: "Spacious 4-Bedroom Duplex",
      location: "Lekki Phase 1, Lagos",
      price: "₦3,500,000",
      priceType: "year",
      purpose: "rent",
      type: "Duplex",
      bedrooms: 4,
      bathrooms: 4,
      imageUrl: "/placeholder.svg?height=200&width=300",
      coordinates: { lat: 6.4531, lng: 3.4 },
    },
    {
      id: "feat_4",
      title: "Commercial Land for Sale",
      location: "Abuja, FCT",
      price: "₦120,000,000",
      priceType: "sale",
      purpose: "sale",
      type: "Land",
      bedrooms: 0,
      bathrooms: 0,
      imageUrl: "/placeholder.svg?height=200&width=300",
      coordinates: { lat: 9.0765, lng: 7.3986 },
    },
  ]
}

export default async function HomePage() {
  const featuredProperties = await getFeaturedProperties()

  return (
    <div className="flex flex-col min-h-screen">
      {/* Hero Section */}
      <section
        className="relative h-[60vh] bg-cover bg-center flex items-center justify-center text-white"
        style={{ backgroundImage: "url('/placeholder.svg?height=800&width=1200')" }}
      >
        <div className="absolute inset-0 bg-black/50" />
        <div className="z-10 text-center p-4">
          <h1 className="text-5xl font-bold mb-4 animate-fade-in-up">Find Your Dream Property in Nigeria</h1>
          <p className="text-xl mb-8 animate-fade-in-up animation-delay-200">
            Explore thousands of properties for rent and sale across the nation.
          </p>
          <div className="flex flex-col md:flex-row gap-4 justify-center animate-fade-in-up animation-delay-400">
            <Button size="lg" className="bg-primary hover:bg-primary/90 text-white">
              <Link href="/properties">Browse Properties</Link>
            </Button>
            <Button size="lg" variant="outline" className="text-white border-white hover:bg-white/20 bg-transparent">
              <Link href="/search">Advanced Search</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* Quick Search Section */}
      <section className="py-12 bg-background">
        <div className="container mx-auto px-4">
          <Card className="p-6 shadow-lg -mt-24 relative z-20">
            <CardHeader className="text-center">
              <CardTitle className="text-3xl font-bold">Quick Search</CardTitle>
              <CardDescription>Find properties by location, type, and purpose.</CardDescription>
            </CardHeader>
            <CardContent>
              <form className="grid grid-cols-1 md:grid-cols-4 gap-4 items-end">
                <div className="space-y-2">
                  <label htmlFor="location" className="text-sm font-medium">
                    Location
                  </label>
                  <Input id="location" placeholder="e.g., Lagos, Abuja" />
                </div>
                <div className="space-y-2">
                  <label htmlFor="purpose" className="text-sm font-medium">
                    Purpose
                  </label>
                  <Select>
                    <SelectTrigger id="purpose">
                      <SelectValue placeholder="Rent or Sale" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="rent">For Rent</SelectItem>
                      <SelectItem value="sale">For Sale</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <div className="space-y-2">
                  <label htmlFor="type" className="text-sm font-medium">
                    Property Type
                  </label>
                  <Select>
                    <SelectTrigger id="type">
                      <SelectValue placeholder="Any Type" />
                    </SelectTrigger>
                    <SelectContent>
                      <SelectItem value="apartment">Apartment</SelectItem>
                      <SelectItem value="house">House</SelectItem>
                      <SelectItem value="duplex">Duplex</SelectItem>
                      <SelectItem value="villa">Villa</SelectItem>
                      <SelectItem value="land">Land</SelectItem>
                      <SelectItem value="commercial">Commercial</SelectItem>
                    </SelectContent>
                  </Select>
                </div>
                <Button type="submit" className="w-full">
                  <Search className="h-4 w-4 mr-2" /> Search
                </Button>
              </form>
            </CardContent>
          </Card>
        </div>
      </section>

      {/* Featured Properties Section */}
      <section className="py-12 bg-muted">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-10">Featured Properties</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6">
            {featuredProperties.map((property) => (
              <PropertyCard key={property.id} property={property} />
            ))}
          </div>
          <div className="text-center mt-10">
            <Button size="lg" variant="outline" asChild>
              <Link href="/properties">View All Properties</Link>
            </Button>
          </div>
        </div>
      </section>

      {/* How It Works Section */}
      <section className="py-12 bg-background">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-10">How It Works</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            <Card className="text-center p-6">
              <Home className="h-12 w-12 text-primary mx-auto mb-4" />
              <CardTitle className="mb-2">1. Find Your Property</CardTitle>
              <CardDescription>
                Use our advanced search and filters to discover properties that match your criteria.
              </CardDescription>
            </Card>
            <Card className="text-center p-6">
              <DollarSign className="h-12 w-12 text-primary mx-auto mb-4" />
              <CardTitle className="mb-2">2. Connect with Agents</CardTitle>
              <CardDescription>
                Directly contact verified agents or property managers for viewings and inquiries.
              </CardDescription>
            </Card>
            <Card className="text-center p-6">
              <MapPin className="h-12 w-12 text-primary mx-auto mb-4" />
              <CardTitle className="mb-2">3. Secure Your Deal</CardTitle>
              <CardDescription>
                Finalize your rent or purchase with confidence, supported by our secure platform.
              </CardDescription>
            </Card>
          </div>
        </div>
      </section>

      {/* Testimonials/Why Choose Us Section */}
      <section className="py-12 bg-muted">
        <div className="container mx-auto px-4">
          <h2 className="text-4xl font-bold text-center mb-10">Why Choose Us?</h2>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
            <Card className="p-6">
              <CardTitle className="mb-4">Extensive Listings</CardTitle>
              <CardDescription>
                Access a vast database of properties across all major Nigerian cities, updated daily.
              </CardDescription>
            </Card>
            <Card className="p-6">
              <CardTitle className="mb-4">Verified Properties & Agents</CardTitle>
              <CardDescription>We ensure all listings and agents are verified for your peace of mind.</CardDescription>
            </Card>
            <Card className="p-6">
              <CardTitle className="mb-4">Smart Recommendations</CardTitle>
              <CardDescription>
                Our AI-powered engine learns your preferences to suggest properties you'll love.
              </CardDescription>
            </Card>
            <Card className="p-6">
              <CardTitle className="mb-4">Seamless Experience</CardTitle>
              <CardDescription>
                From search to secure payment, our platform makes your property journey smooth.
              </CardDescription>
            </Card>
          </div>
        </div>
      </section>

      {/* Call to Action / Newsletter */}
      <section className="py-12 bg-primary text-primary-foreground">
        <div className="container mx-auto px-4 text-center">
          <h2 className="text-4xl font-bold mb-4">Stay Updated</h2>
          <p className="text-xl mb-8">
            Subscribe to our newsletter for the latest property listings and market insights.
          </p>
          <form className="flex flex-col sm:flex-row gap-4 max-w-lg mx-auto">
            <Input
              type="email"
              placeholder="Enter your email"
              className="flex-1 bg-primary-foreground/20 border-primary-foreground/50 text-primary-foreground placeholder:text-primary-foreground/70"
            />
            <Button type="submit" variant="secondary" className="text-primary">
              Subscribe
            </Button>
          </form>
        </div>
      </section>

      {/* Recent Searches and Recommended Properties */}
      <section className="py-12 bg-background">
        <div className="container mx-auto px-4 space-y-12">
          <RecentSearches />
          <RecommendedProperties />
        </div>
      </section>
    </div>
  )
}
