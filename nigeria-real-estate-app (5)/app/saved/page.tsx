import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>eader, <PERSON>Title } from "@/components/ui/card"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Heart, Trash2 } from "lucide-react"
import Image from "next/image"

export default function SavedPropertiesPage() {
  const savedProperties = [
    {
      id: 1,
      title: "Luxury 4-Bedroom Duplex",
      location: "Ikoyi, Lagos",
      price: "₦150,000,000",
      imageUrl: "/placeholder.svg?height=200&width=300",
    },
    {
      id: 2,
      title: "Spacious 3-Bedroom Apartment",
      location: "Victoria Island, Lagos",
      price: "₦5,000,000/year",
      imageUrl: "/placeholder.svg?height=200&width=300",
    },
    {
      id: 3,
      title: "Commercial Land for Sale",
      location: "Lekki, Lagos",
      price: "₦80,000,000",
      imageUrl: "/placeholder.svg?height=200&width=300",
    },
  ]

  return (
    <div className="p-4">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <Heart className="h-6 w-6" /> Saved Properties
          </CardTitle>
        </CardHeader>
        <CardContent>
          {savedProperties.length === 0 ? (
            <p className="text-muted-foreground">You haven't saved any properties yet.</p>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {savedProperties.map((property) => (
                <Card key={property.id} className="overflow-hidden">
                  <Image
                    src={property.imageUrl || "/placeholder.svg"}
                    alt={property.title}
                    width={300}
                    height={200}
                    className="w-full h-48 object-cover"
                  />
                  <div className="p-4">
                    <h3 className="text-lg font-semibold">{property.title}</h3>
                    <p className="text-sm text-muted-foreground">{property.location}</p>
                    <p className="text-xl font-bold mt-2">{property.price}</p>
                    <div className="flex justify-end mt-4">
                      <Button variant="destructive" size="sm">
                        <Trash2 className="h-4 w-4 mr-2" /> Remove
                      </Button>
                    </div>
                  </div>
                </Card>
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
