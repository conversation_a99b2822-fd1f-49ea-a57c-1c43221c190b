"use client"

import React, { createContext, useContext, useState, useCallback, type ReactNode } from "react"

interface AuthModalContextType {
  isOpen: boolean
  view: "login" | "signup"
  openModal: (view?: "login" | "signup") => void
  closeModal: () => void
  setView: (view: "login" | "signup") => void
}

const AuthModalContext = createContext<AuthModalContextType | undefined>(undefined)

export function AuthModalProvider({ children }: { children: ReactNode }) {
  const [isOpen, setIsOpen] = useState(false)
  const [view, setView] = useState<"login" | "signup">("login")

  const openModal = useCallback((initialView: "login" | "signup" = "login") => {
    setView(initialView)
    setIsOpen(true)
  }, [])

  const closeModal = useCallback(() => {
    setIsOpen(false)
  }, [])

  const contextValue = React.useMemo(
    () => ({
      isOpen,
      view,
      openModal,
      closeModal,
      setView,
    }),
    [isOpen, view, openModal, closeModal, setView],
  )

  return <AuthModalContext.Provider value={contextValue}>{children}</AuthModalContext.Provider>
}

export function useAuthModal() {
  const context = useContext(AuthModalContext)
  if (context === undefined) {
    throw new Error("useAuthModal must be used within an AuthModalProvider")
  }
  return context
}
