# RentE@sy Flexible Payment System Documentation

## 📋 Overview

RentE@sy is an innovative flexible payment system integrated into the NaijaHomes real estate app, designed specifically for the Nigerian market. It allows tenants to rent properties with manageable monthly payment plans instead of paying large annual rents upfront.

## 🎯 System Objectives

### Primary Goals
- **Accessibility**: Make quality housing accessible to more Nigerians
- **Flexibility**: Offer multiple payment plan options to suit different income levels
- **Trust**: Build confidence through transparent pricing and secure transactions
- **Compliance**: Ensure full compliance with Nigerian financial regulations

### Target Users
- **Young Professionals**: Starting their careers with limited savings
- **Small Business Owners**: Managing cash flow challenges
- **Students**: Needing affordable accommodation options
- **Families**: Looking for budget-friendly housing solutions

## 🏗️ System Architecture

### Core Components

#### 1. Application Management System
\`\`\`typescript
interface RentEasyApplication {
  id: string
  applicantId: string
  propertyId: string
  planDetails: PaymentPlan
  personalInfo: PersonalInformation
  financialInfo: FinancialInformation
  documents: Document[]
  status: ApplicationStatus
  riskAssessment: RiskScore
  createdAt: Date
  updatedAt: Date
}
\`\`\`

#### 2. Payment Plan Engine
\`\`\`typescript
interface PaymentPlan {
  duration: 6 | 12 | 24 // months
  monthlyAmount: number
  totalAmount: number
  interestRate: number
  interestAmount: number
  processingFee: number
  latePaymentFee: number
  earlyPaymentDiscount: number
}
\`\`\`

#### 3. Risk Assessment Module
\`\`\`typescript
interface RiskAssessment {
  creditScore: number
  incomeVerification: boolean
  employmentStatus: EmploymentType
  bankStatementAnalysis: BankAnalysis
  bvnVerification: BVNStatus
  previousRentalHistory: RentalHistory[]
  overallRiskLevel: 'LOW' | 'MEDIUM' | 'HIGH'
  recommendedAction: 'APPROVE' | 'REVIEW' | 'REJECT'
}
\`\`\`

## 💰 Payment Plan Structure

### Plan Options

#### 6-Month Plan
- **Interest Rate**: 0% (promotional)
- **Monthly Payment**: Higher amount
- **Target Audience**: High-income earners
- **Benefits**: No interest charges, shorter commitment

#### 12-Month Plan
- **Interest Rate**: 8-12% annually
- **Monthly Payment**: Moderate amount
- **Target Audience**: Middle-income earners
- **Benefits**: Balanced payments, reasonable interest

#### 24-Month Plan
- **Interest Rate**: 15-20% annually
- **Monthly Payment**: Lower amount
- **Target Audience**: Lower-income earners
- **Benefits**: Most affordable monthly payments

### Calculation Formula

\`\`\`typescript
function calculatePaymentPlan(
  annualRent: number,
  duration: number,
  interestRate: number
): PaymentPlan {
  const principal = annualRent
  const monthlyInterestRate = interestRate / 12 / 100
  const numberOfPayments = duration
  
  // Calculate monthly payment using amortization formula
  const monthlyPayment = principal * 
    (monthlyInterestRate * Math.pow(1 + monthlyInterestRate, numberOfPayments)) /
    (Math.pow(1 + monthlyInterestRate, numberOfPayments) - 1)
  
  const totalAmount = monthlyPayment * numberOfPayments
  const interestAmount = totalAmount - principal
  
  return {
    duration,
    monthlyAmount: Math.round(monthlyPayment),
    totalAmount: Math.round(totalAmount),
    interestRate,
    interestAmount: Math.round(interestAmount),
    processingFee: Math.round(principal * 0.01), // 1% processing fee
    latePaymentFee: Math.round(monthlyPayment * 0.05), // 5% late fee
    earlyPaymentDiscount: Math.round(interestAmount * 0.1) // 10% discount for early payment
  }
}
\`\`\`

## 🔍 Application Process

### Step 1: Property Selection
- Browse RentE@sy eligible properties
- View payment plan options
- Compare monthly payments vs. annual rent
- Select preferred payment plan

### Step 2: Application Submission
\`\`\`typescript
interface ApplicationForm {
  // Personal Information
  fullName: string
  dateOfBirth: Date
  phoneNumber: string
  emailAddress: string
  bvn: string
  nationalId: string
  
  // Employment Information
  employmentStatus: 'EMPLOYED' | 'SELF_EMPLOYED' | 'STUDENT' | 'UNEMPLOYED'
  employerName?: string
  jobTitle?: string
  workAddress?: string
  monthlyIncome: number
  incomeProof: Document[]
  
  // Financial Information
  bankName: string
  accountNumber: string
  bankStatement: Document[]
  otherIncome?: number
  monthlyExpenses: number
  existingLoans?: LoanInformation[]
  
  // References
  references: Reference[]
  
  // Preferred Plan
  selectedPlan: PaymentPlan
  moveInDate: Date
}
\`\`\`

### Step 3: Document Verification
- **Identity Verification**: BVN and National ID validation
- **Income Verification**: Bank statements and employment letters
- **Address Verification**: Utility bills or official documents
- **Reference Checks**: Contact provided references

### Step 4: Risk Assessment
\`\`\`typescript
function assessRisk(application: ApplicationForm): RiskAssessment {
  let score = 0
  
  // Income to rent ratio (40% weight)
  const incomeRatio = application.monthlyIncome / application.selectedPlan.monthlyAmount
  if (incomeRatio >= 3) score += 40
  else if (incomeRatio >= 2.5) score += 30
  else if (incomeRatio >= 2) score += 20
  else score += 10
  
  // Employment status (25% weight)
  switch (application.employmentStatus) {
    case 'EMPLOYED': score += 25; break
    case 'SELF_EMPLOYED': score += 20; break
    case 'STUDENT': score += 15; break
    case 'UNEMPLOYED': score += 5; break
  }
  
  // BVN verification (20% weight)
  if (application.bvnVerified) score += 20
  
  // Bank statement analysis (15% weight)
  const avgBalance = analyzeBankStatement(application.bankStatement)
  if (avgBalance >= application.selectedPlan.monthlyAmount * 3) score += 15
  else if (avgBalance >= application.selectedPlan.monthlyAmount * 2) score += 10
  else score += 5
  
  return {
    creditScore: score,
    overallRiskLevel: score >= 80 ? 'LOW' : score >= 60 ? 'MEDIUM' : 'HIGH',
    recommendedAction: score >= 70 ? 'APPROVE' : score >= 50 ? 'REVIEW' : 'REJECT'
  }
}
\`\`\`

### Step 5: Approval Decision
- **Automatic Approval**: Low-risk applications (score ≥ 70)
- **Manual Review**: Medium-risk applications (score 50-69)
- **Automatic Rejection**: High-risk applications (score < 50)

## 💳 Payment Management

### Payment Schedule
\`\`\`typescript
interface PaymentSchedule {
  planId: string
  payments: ScheduledPayment[]
  totalAmount: number
  paidAmount: number
  remainingAmount: number
  nextPaymentDate: Date
  status: 'ACTIVE' | 'COMPLETED' | 'DEFAULTED' | 'SUSPENDED'
}

interface ScheduledPayment {
  id: string
  dueDate: Date
  amount: number
  status: 'PENDING' | 'PAID' | 'OVERDUE' | 'FAILED'
  paidDate?: Date
  paidAmount?: number
  paymentMethod?: string
  transactionId?: string
  lateFee?: number
}
\`\`\`

### Payment Processing
\`\`\`typescript
async function processPayment(
  paymentId: string,
  amount: number,
  paymentMethod: PaymentMethod
): Promise<PaymentResult> {
  try {
    // Validate payment
    const payment = await getScheduledPayment(paymentId)
    if (!payment) throw new Error('Payment not found')
    if (payment.status !== 'PENDING') throw new Error('Payment already processed')
    
    // Process payment based on method
    let transactionResult: TransactionResult
    
    switch (paymentMethod.type) {
      case 'BANK_TRANSFER':
        transactionResult = await processBankTransfer(amount, paymentMethod.details)
        break
      case 'CARD_PAYMENT':
        transactionResult = await processCardPayment(amount, paymentMethod.details)
        break
      case 'USSD':
        transactionResult = await processUSSDPayment(amount, paymentMethod.details)
        break
      default:
        throw new Error('Unsupported payment method')
    }
    
    // Update payment record
    await updatePaymentStatus(paymentId, {
      status: 'PAID',
      paidDate: new Date(),
      paidAmount: amount,
      transactionId: transactionResult.transactionId,
      paymentMethod: paymentMethod.type
    })
    
    // Send confirmation
    await sendPaymentConfirmation(payment.planId, amount)
    
    return {
      success: true,
      transactionId: transactionResult.transactionId,
      message: 'Payment processed successfully'
    }
    
  } catch (error) {
    await logPaymentError(paymentId, error)
    return {
      success: false,
      error: error.message
    }
  }
}
\`\`\`

### Payment Methods Integration

#### Bank Transfer
\`\`\`typescript
interface BankTransferDetails {
  bankCode: string
  accountNumber: string
  accountName: string
  narration: string
  reference: string
}

async function processBankTransfer(
  amount: number,
  details: BankTransferDetails
): Promise<TransactionResult> {
  // Integration with Nigerian banks via Paystack or Flutterwave
  const response = await paystackAPI.initiateTransfer({
    amount: amount * 100, // Convert to kobo
    recipient: details.accountNumber,
    reason: details.narration,
    reference: details.reference
  })
  
  return {
    transactionId: response.data.reference,
    status: response.data.status,
    amount: amount
  }
}
\`\`\`

#### USSD Payment
\`\`\`typescript
interface USSDDetails {
  bankCode: string
  phoneNumber: string
  ussdCode: string
}

async function generateUSSDCode(
  amount: number,
  reference: string,
  bankCode: string
): Promise<string> {
  // Generate bank-specific USSD code
  const bankUSSDCodes = {
    '058': '*737', // GTBank
    '011': '*826', // First Bank
    '033': '*894', // UBA
    '214': '*919', // FCMB
    // Add more banks
  }
  
  const baseCode = bankUSSDCodes[bankCode]
  return `${baseCode}*000*${amount}*${reference}#`
}
\`\`\`

## 📱 Payment Reminder System

### Reminder Types
\`\`\`typescript
interface ReminderCampaign {
  id: string
  name: string
  type: 'AUTOMATIC' | 'MANUAL'
  trigger: ReminderTrigger
  channels: CommunicationChannel[]
  template: MessageTemplate
  status: 'ACTIVE' | 'PAUSED' | 'DRAFT'
  targetAudience: AudienceFilter
  schedule: ReminderSchedule
}

type ReminderTrigger = 
  | '7_DAYS_BEFORE'
  | '3_DAYS_BEFORE'
  | '1_DAY_BEFORE'
  | 'DUE_DATE'
  | '1_DAY_OVERDUE'
  | '3_DAYS_OVERDUE'
  | '7_DAYS_OVERDUE'
  | 'PAYMENT_RECEIVED'

type CommunicationChannel = 'SMS' | 'EMAIL' | 'PUSH' | 'CALL' | 'WHATSAPP'
\`\`\`

### Message Templates
\`\`\`typescript
interface MessageTemplate {
  id: string
  name: string
  channel: CommunicationChannel
  category: 'PRE_DUE' | 'OVERDUE' | 'CONFIRMATION' | 'ESCALATION'
  subject?: string // For email
  content: string
  variables: TemplateVariable[]
  personalization: PersonalizationRule[]
}

interface TemplateVariable {
  name: string
  type: 'STRING' | 'NUMBER' | 'DATE' | 'CURRENCY'
  required: boolean
  defaultValue?: string
}

// Example templates
const reminderTemplates = {
  friendlyReminder: {
    sms: "Hi {name}! Your RentE@sy payment of {amount} is due on {due_date}. Pay easily via our app. Thank you! 🏠",
    email: {
      subject: "Friendly Reminder: Payment Due in {days_until_due} Days",
      content: `
        Dear {name},
        
        This is a friendly reminder that your RentE@sy payment is due soon.
        
        Payment Details:
        - Property: {property_name}
        - Amount: {amount}
        - Due Date: {due_date}
        - Days Remaining: {days_until_due}
        
        You can make your payment through:
        - Mobile App: {app_link}
        - Website: {website_link}
        - Bank Transfer: {bank_details}
        - USSD: {ussd_code}
        
        Thank you for choosing RentE@sy!
        
        Best regards,
        The NaijaHomes Team
      `
    }
  },
  
  urgentNotice: {
    sms: "URGENT: Your RentE@sy payment of {amount} is {days_overdue} days overdue. Please pay immediately to avoid penalties. Contact: {support_phone}",
    email: {
      subject: "URGENT: Overdue Payment - Immediate Action Required",
      content: `
        Dear {name},
        
        Your RentE@sy payment is now overdue and requires immediate attention.
        
        Overdue Details:
        - Property: {property_name}
        - Amount: {amount}
        - Original Due Date: {due_date}
        - Days Overdue: {days_overdue}
        - Late Fee: {late_fee}
        - Total Amount Due: {total_due}
        
        To avoid further penalties and potential legal action, please make your payment immediately.
        
        If you're experiencing financial difficulties, please contact our support team at {support_phone} or {support_email} to discuss payment arrangements.
        
        Payment Methods:
        - Mobile App: {app_link}
        - Bank Transfer: {bank_details}
        - USSD: {ussd_code}
        
        Urgent attention required.
        
        NaijaHomes Collections Team
      `
    }
  }
}
\`\`\`

### Automated Reminder Workflow
\`\`\`typescript
async function processReminderCampaigns(): Promise<void> {
  const activeCampaigns = await getReminderCampaigns({ status: 'ACTIVE' })
  
  for (const campaign of activeCampaigns) {
    const eligiblePayments = await getEligiblePayments(campaign.trigger)
    
    for (const payment of eligiblePayments) {
      const tenant = await getTenantDetails(payment.tenantId)
      const property = await getPropertyDetails(payment.propertyId)
      
      // Personalize message
      const personalizedMessage = await personalizeMessage(
        campaign.template,
        { tenant, property, payment }
      )
      
      // Send through selected channels
      for (const channel of campaign.channels) {
        await sendReminder(channel, tenant.contactInfo, personalizedMessage)
      }
      
      // Log reminder sent
      await logReminderSent({
        campaignId: campaign.id,
        paymentId: payment.id,
        tenantId: tenant.id,
        channel,
        sentAt: new Date()
      })
    }
  }
}

async function personalizeMessage(
  template: MessageTemplate,
  context: ReminderContext
): Promise<string> {
  let message = template.content
  
  // Replace variables
  const variables = {
    name: context.tenant.fullName,
    amount: formatCurrency(context.payment.amount),
    due_date: formatDate(context.payment.dueDate),
    days_until_due: calculateDaysUntilDue(context.payment.dueDate),
    days_overdue: calculateDaysOverdue(context.payment.dueDate),
    property_name: context.property.title,
    late_fee: formatCurrency(context.payment.lateFee || 0),
    total_due: formatCurrency(context.payment.amount + (context.payment.lateFee || 0)),
    support_phone: '+234 800 NAIJA HOMES',
    support_email: '<EMAIL>',
    app_link: 'https://app.naijahomes.com/renteasy/payment',
    ussd_code: await generateUSSDCode(context.payment.amount, context.payment.id, context.tenant.bankCode)
  }
  
  // Replace all variables in the message
  for (const [key, value] of Object.entries(variables)) {
    message = message.replace(new RegExp(`{${key}}`, 'g'), value.toString())
  }
  
  return message
}
\`\`\`

## 📊 Analytics and Reporting

### Key Performance Indicators (KPIs)
\`\`\`typescript
interface RentEasyKPIs {
  // Application Metrics
  totalApplications: number
  approvalRate: number
  averageProcessingTime: number // in hours
  applicationsByRiskLevel: Record<RiskLevel, number>
  
  // Payment Metrics
  totalActivePlans: number
  totalRevenue: number
  averageMonthlyPayment: number
  paymentSuccessRate: number
  
  // Default Metrics
  defaultRate: number
  averageDaysToDefault: number
  recoveryRate: number
  writeOffRate: number
  
  // Customer Metrics
  customerSatisfactionScore: number
  netPromoterScore: number
  customerRetentionRate: number
  averageCustomerLifetime: number
}

async function generateKPIReport(
  startDate: Date,
  endDate: Date
): Promise<RentEasyKPIs> {
  const applications = await getApplications({ startDate, endDate })
  const payments = await getPayments({ startDate, endDate })
  const plans = await getActivePlans()
  
  return {
    totalApplications: applications.length,
    approvalRate: calculateApprovalRate(applications),
    averageProcessingTime: calculateAverageProcessingTime(applications),
    applicationsByRiskLevel: groupByRiskLevel(applications),
    
    totalActivePlans: plans.length,
    totalRevenue: calculateTotalRevenue(payments),
    averageMonthlyPayment: calculateAveragePayment(plans),
    paymentSuccessRate: calculatePaymentSuccessRate(payments),
    
    defaultRate: calculateDefaultRate(plans),
    averageDaysToDefault: calculateAverageDaysToDefault(plans),
    recoveryRate: calculateRecoveryRate(plans),
    writeOffRate: calculateWriteOffRate(plans),
    
    customerSatisfactionScore: await getCustomerSatisfactionScore(),
    netPromoterScore: await getNetPromoterScore(),
    customerRetentionRate: calculateRetentionRate(plans),
    averageCustomerLifetime: calculateAverageLifetime(plans)
  }
}
\`\`\`

### Risk Analysis
\`\`\`typescript
interface RiskAnalysis {
  riskDistribution: Record<RiskLevel, number>
  defaultPrediction: DefaultPrediction[]
  riskFactors: RiskFactor[]
  recommendations: RiskRecommendation[]
}

interface DefaultPrediction {
  planId: string
  tenantId: string
  currentRiskScore: number
  predictedDefaultProbability: number
  recommendedAction: 'MONITOR' | 'CONTACT' | 'RESTRUCTURE' | 'ESCALATE'
  factors: string[]
}

async function analyzeRisk(): Promise<RiskAnalysis> {
  const activePlans = await getActivePlans()
  const predictions: DefaultPrediction[] = []
  
  for (const plan of activePlans) {
    const paymentHistory = await getPaymentHistory(plan.id)
    const tenantProfile = await getTenantProfile(plan.tenantId)
    
    const riskScore = calculateCurrentRiskScore(plan, paymentHistory, tenantProfile)
    const defaultProbability = predictDefaultProbability(riskScore, paymentHistory)
    
    predictions.push({
      planId: plan.id,
      tenantId: plan.tenantId,
      currentRiskScore: riskScore,
      predictedDefaultProbability: defaultProbability,
      recommendedAction: getRecommendedAction(defaultProbability),
      factors: identifyRiskFactors(plan, paymentHistory, tenantProfile)
    })
  }
  
  return {
    riskDistribution: calculateRiskDistribution(predictions),
    defaultPrediction: predictions.filter(p => p.predictedDefaultProbability > 0.3),
    riskFactors: identifyTopRiskFactors(predictions),
    recommendations: generateRiskRecommendations(predictions)
  }
}
\`\`\`

## 🔒 Security and Compliance

### Data Security
\`\`\`typescript
interface SecurityMeasures {
  encryption: {
    atRest: 'AES-256'
    inTransit: 'TLS 1.3'
    keyManagement: 'AWS KMS'
  }
  
  authentication: {
    multiFactorAuth: boolean
    biometricAuth: boolean
    sessionTimeout: number // minutes
    passwordPolicy: PasswordPolicy
  }
  
  authorization: {
    roleBasedAccess: boolean
    principleOfLeastPrivilege: boolean
    auditLogging: boolean
  }
  
  compliance: {
    pciDss: boolean
    gdpr: boolean
    ndpr: boolean // Nigeria Data Protection Regulation
    cbn: boolean // Central Bank of Nigeria guidelines
  }
}

interface AuditLog {
  id: string
  userId: string
  action: string
  resource: string
  timestamp: Date
  ipAddress: string
  userAgent: string
  result: 'SUCCESS' | 'FAILURE'
  details: Record<string, any>
}

async function logAuditEvent(
  userId: string,
  action: string,
  resource: string,
  details: Record<string, any>
): Promise<void> {
  await createAuditLog({
    userId,
    action,
    resource,
    timestamp: new Date(),
    ipAddress: getCurrentIPAddress(),
    userAgent: getCurrentUserAgent(),
    result: 'SUCCESS',
    details
  })
}
\`\`\`

### Regulatory Compliance

#### Central Bank of Nigeria (CBN) Guidelines
- **Know Your Customer (KYC)**: BVN verification and identity validation
- **Anti-Money Laundering (AML)**: Transaction monitoring and reporting
- **Consumer Protection**: Transparent pricing and fair lending practices
- **Data Protection**: Secure handling of financial information

#### Nigeria Data Protection Regulation (NDPR)
- **Consent Management**: Explicit consent for data processing
- **Data Minimization**: Collect only necessary information
- **Right to Access**: Users can request their data
- **Right to Deletion**: Users can request data deletion
- **Data Breach Notification**: Report breaches within 72 hours

## 🚀 Future Enhancements

### Planned Features
1. **AI-Powered Risk Assessment**: Machine learning models for better risk prediction
2. **Blockchain Integration**: Immutable payment records and smart contracts
3. **Voice Payments**: Integration with voice assistants for payment commands
4. **Cryptocurrency Support**: Accept Bitcoin and other cryptocurrencies
5. **Insurance Integration**: Property and payment protection insurance
6. **Credit Bureau Integration**: Report payment history to improve credit scores
7. **Rent-to-Own Options**: Pathway to property ownership through extended plans
8. **Social Features**: Tenant communities and referral programs

### Technical Roadmap
- **Q2 2024**: Enhanced mobile app with offline capabilities
- **Q3 2024**: AI risk assessment and fraud detection
- **Q4 2024**: Blockchain payment records and smart contracts
- **Q1 2025**: Voice payment integration and IoT connectivity
- **Q2 2025**: International expansion and multi-currency support

## 📞 Support and Maintenance

### Customer Support
- **24/7 Helpline**: +234 800 NAIJA HOMES
- **Live Chat**: In-app messaging system
- **Email Support**: <EMAIL>
- **WhatsApp**: +234 901 RENT EASY
- **Self-Service**: Comprehensive FAQ and knowledge base

### System Maintenance
- **Regular Updates**: Monthly feature releases and bug fixes
- **Security Patches**: Immediate deployment of security updates
- **Performance Monitoring**: 24/7 system monitoring and alerting
- **Backup and Recovery**: Daily backups with 99.9% uptime guarantee
- **Disaster Recovery**: Multi-region deployment for business continuity

---

**RentE@sy - Making Nigerian Real Estate Accessible to Everyone**

*This documentation is maintained by the NaijaHomes development team and is updated regularly to reflect system changes and improvements.*
