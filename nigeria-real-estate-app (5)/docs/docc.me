I'll create comprehensive documentation for the Real Estate Property Management System.

`<CodeProject id="nigeria-real-estate-app">\`\`\``markdown file="README.md"
...
\`\`\`

I did not output the full code. I will fix this.

\`\`\`markdown
# Nigeria Real Estate Management System

A comprehensive real estate management platform designed for property managers, landlords, tenants, and buyers in Nigeria. The system features advanced property search, management tools, and integrated services.

## 🏠 Overview

This platform provides a complete solution for real estate management in Nigeria, featuring:

- **Advanced Property Search**: AI-powered search with personalized recommendations
- **Property Management**: Complete property lifecycle management
- **Tenant Management**: Comprehensive tenant tracking and lease management
- **Payment Processing**: Integrated rent collection and payment tracking
- **Multi-user Support**: Different interfaces for managers, landlords, tenants, and buyers

## 🚀 Features

### Core Features
- Advanced property search with ML-powered recommendations
- Interactive map-based property discovery
- Property comparison tools
- Comprehensive property management dashboard
- Tenant management and lease tracking
- Payment processing and rent collection
- Document management system
- Real-time notifications and alerts

### User Roles
- **Property Managers**: Full property and tenant management
- **Landlords**: Property oversight and tenant communication
- **Tenants**: Rent payments and maintenance requests
- **Buyers/Renters**: Property search and discovery

## 📋 Table of Contents

1. [Installation](#installation)
2. [Architecture](#architecture)
3. [User Guide](#user-guide)
4. [API Documentation](#api-documentation)
5. [Development Guide](#development-guide)
6. [Deployment](#deployment)
7. [Contributing](#contributing)
8. [Data Models](#data-models)

## 🛠 Installation

### Prerequisites
- Node.js 18.0 or higher
- npm or yarn package manager

### Quick Start
\`\`\`bash
# Clone the repository
git clone https://github.com/your-org/nigeria-real-estate-app.git

# Navigate to project directory
cd nigeria-real-estate-app

# Install dependencies
npm install

# Start development server
npm run dev
\`\`\`

### Environment Variables
Create a `.env.local` file in the root directory:

\`\`\`env
# Database
DATABASE_URL="your-database-connection-string"

# Authentication
NEXTAUTH_SECRET="your-nextauth-secret"
NEXTAUTH_URL="http://localhost:3000"

# File Storage
BLOB_READ_WRITE_TOKEN="your-blob-storage-token"

# Payment Gateway
PAYSTACK_SECRET_KEY="your-paystack-secret"
PAYSTACK_PUBLIC_KEY="your-paystack-public-key"

# External APIs
GOOGLE_MAPS_API_KEY="your-google-maps-key"
\`\`\`

## 🏗 Architecture

### Technology Stack
- **Framework**: Next.js 14 with App Router
- **Styling**: Tailwind CSS + shadcn/ui components
- **State Management**: React Hooks + Context API
- **Database**: PostgreSQL with Prisma ORM
- **Authentication**: NextAuth.js
- **File Storage**: Vercel Blob Storage
- **Payments**: Paystack Integration
- **Maps**: Google Maps API

### Project Structure
\`\`\`
nigeria-real-estate-app/
├── app/                    # Next.js App Router pages
│   ├── admin/             # Admin dashboard
│   ├── dashboard/         # User dashboard
│   ├── landlord/          # Landlord interface
│   ├── manager/           # Property manager interface
│   ├── properties/        # Property listings
│   ├── search/            # Advanced search
│   └── api/               # API routes
├── components/            # Reusable components
│   ├── ui/                # shadcn/ui components
│   └── ...
├── hooks/                 # Custom React hooks
├── lib/                   # Utility functions
├── docs/                  # Documentation
└── public/                # Static assets
\`\`\`

## 📖 User Guide

### For Property Managers

#### Dashboard Overview
The manager dashboard provides a comprehensive overview of your property portfolio:

- **Property Statistics**: Total properties, occupancy rates, revenue
- **Recent Activity**: Latest tenant applications, payments, maintenance requests
- **Quick Actions**: Add property, add tenant, record payment
- **Notifications**: Overdue payments, lease renewals, maintenance alerts

#### Managing Properties

##### Adding a New Property
1. Navigate to **Manager > Properties > Add Property**
2. Fill in property details:
   - Basic information (title, address, type)
   - Owner information and contacts
   - Property specifications (bedrooms, bathrooms, size)
   - Features and amenities
   - Photos and documents
3. Set rental/sale price and availability
4. Submit for approval

##### Editing Properties
1. Go to **Manager > Properties**
2. Find the property and click **Edit**
3. Update any information as needed
4. Save changes

##### Property Status Management
- **Available**: Property is ready for new tenants
- **Occupied**: Property has current tenants
- **Maintenance**: Property is under repair
- **Vacant**: Property is empty but not ready for rent

#### Managing Tenants

##### Adding Tenants
1. Navigate to **Manager > Tenants > Add Tenant**
2. Enter tenant information:
   - Personal details (name, phone, email)
   - Employment information
   - Emergency contacts
   - Previous rental history
3. Associate with a property
4. Set lease terms and rent amount

##### Lease Management
- Create and manage lease agreements
- Track lease start and end dates
- Set up automatic renewal reminders
- Generate lease documents

#### Payment Management

##### Recording Payments
1. Go to **Manager > Payments**
2. Click **Record Payment**
3. Select tenant and payment details
4. Upload payment proof if needed
5. Generate receipt

##### Tracking Overdue Payments
- View overdue payments dashboard
- Send automated reminders
- Track payment history
- Generate payment reports

### For Property Seekers

#### Advanced Search
The search system offers powerful filtering and personalization:

##### Basic Search
1. Enter location or property name in search bar
2. Select purpose (rent/buy)
3. Choose property type
4. Set budget range
5. Specify number of bedrooms

##### Advanced Filtering
Access advanced filters for:
- **Property Features**: Furnished, parking, security, etc.
- **Nearby Amenities**: Schools, hospitals, shopping centers
- **Property Conditions**: New, renovated, verified only
- **Special Features**: Virtual tours, immediate availability

##### Map Search
1. Switch to map view
2. Use map controls to navigate
3. Click property markers for quick details
4. Apply map-specific filters

##### Saving and Comparing Properties
- **Save Properties**: Click heart icon to save favorites
- **Compare Properties**: Add up to 3 properties for side-by-side comparison
- **Recent Searches**: Quick access to previous searches

#### Personalized Recommendations
The system learns from your behavior to provide better recommendations:
- Based on search history
- Similar to saved properties
- Matching your preferred locations and price range
- Considering your viewing patterns

## 🔧 API Documentation

### Authentication Endpoints

#### POST `/api/auth/login`
User authentication

**Request Body:**
\`\`\`json
{
  "email": "<EMAIL>",
  "password": "password123"
}
\`\`\`

**Response:**
\`\`\`json
{
  "success": true,
  "user": {
    "id": "user_id",
    "email": "<EMAIL>",
    "role": "manager"
  },
  "token": "jwt_token"
}
\`\`\`

### Property Endpoints

#### GET `/api/properties`
Retrieve properties with filtering

**Query Parameters:**
- `search`: Search query
- `location`: Property location
- `type`: Property type
- `minPrice`: Minimum price
- `maxPrice`: Maximum price
- `bedrooms`: Number of bedrooms
- `purpose`: "rent" or "sale"
- `page`: Page number
- `limit`: Results per page

**Response:**
\`\`\`json
{
  "properties": [
    {
      "id": "prop_123",
      "title": "Modern 3-Bedroom Apartment",
      "location": "Victoria Island, Lagos",
      "price": 2500000,
      "type": "Apartment",
      "bedrooms": 3,
      "bathrooms": 2,
      "features": ["Furnished", "Parking", "Security"],
      "images": ["url1", "url2"],
      "agent": {
        "name": "John Doe",
        "phone": "+234 ************"
      }
    }
  ],
  "pagination": {
    "total": 150,
    "page": 1,
    "limit": 20,
    "pages": 8
  }
}
\`\`\`

#### POST `/api/properties`
Create a new property (Manager only)

**Request Body:**
\`\`\`json
{
  "title": "Luxury Villa",
  "location": "Banana Island, Lagos",
  "price": 150000000,
  "type": "Villa",
  "purpose": "sale",
  "bedrooms": 5,
  "bathrooms": 6,
  "size": 450,
  "features": ["Swimming Pool", "Garden", "Security"],
  "description": "Luxury waterfront villa...",
  "owner": {
    "name": "Property Owner",
    "phone": "+234 ************",
    "email": "<EMAIL>"
  }
}
\`\`\`

### Tenant Endpoints

#### GET `/api/tenants`
Retrieve tenants (Manager only)

#### POST `/api/tenants`
Add new tenant (Manager only)

#### PUT `/api/tenants/:id`
Update tenant information (Manager only)

### Payment Endpoints

#### GET `/api/payments`
Retrieve payment records

#### POST `/api/payments`
Record new payment

#### GET `/api/payments/overdue`
Get overdue payments

### Search Endpoints

#### POST `/api/search`
Advanced property search with ML scoring

#### GET `/api/search/recommendations/:userId`
Get personalized property recommendations

#### POST `/api/search/save`
Save search criteria for alerts

## 👨‍💻 Development Guide

### Getting Started

1. **Fork and Clone**
   \`\`\`bash
   git clone https://github.com/your-username/nigeria-real-estate-app.git
   cd nigeria-real-estate-app
   \`\`\`

2. **Install Dependencies**
   \`\`\`bash
   npm install
   \`\`\`

3. **Set up Environment**
   Copy `.env.example` to `.env.local` and fill in your values

4. **Run Development Server**
   \`\`\`bash
   npm run dev
   \`\`\`

### Code Structure

#### Components
- Use TypeScript for all components
- Follow the established naming convention (kebab-case for files)
- Use shadcn/ui components when possible
- Implement proper error boundaries

#### Hooks
Custom hooks are located in `/hooks/` directory:
- `usePropertySearch`: Advanced property search functionality
- `useAuth`: Authentication state management
- `useTenants`: Tenant management operations
- `usePayments`: Payment tracking and processing

#### Utilities
Common utilities in `/lib/` directory:
- `search-algorithm.ts`: ML-powered search scoring
- `utils.ts`: General utility functions
- `validations.ts`: Form validation schemas

### Adding New Features

#### Creating a New Page
1. Create page file in appropriate `/app/` subdirectory
2. Add loading.tsx for loading states
3. Implement error.tsx for error handling
4. Update navigation if needed

#### Adding API Endpoints
1. Create route handler in `/app/api/`
2. Implement proper authentication checks
3. Add input validation
4. Include error handling
5. Document the endpoint

#### Component Development Guidelines
- Use TypeScript interfaces for props
- Implement proper accessibility (ARIA labels, keyboard navigation)
- Add loading and error states
- Include responsive design
- Write unit tests for complex logic

### Testing

#### Running Tests
\`\`\`bash
# Run all tests
npm test

# Run tests in watch mode
npm run test:watch

# Run tests with coverage
npm run test:coverage
\`\`\`

#### Writing Tests
- Use Jest and React Testing Library
- Test user interactions, not implementation details
- Mock external dependencies
- Aim for 80%+ code coverage

### Database Schema

#### Properties Table
\`\`\`sql
CREATE TABLE properties (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  title VARCHAR(255) NOT NULL,
  description TEXT,
  location VARCHAR(255) NOT NULL,
  price DECIMAL(15,2) NOT NULL,
  property_type VARCHAR(50) NOT NULL,
  purpose VARCHAR(10) NOT NULL CHECK (purpose IN ('rent', 'sale')),
  bedrooms INTEGER,
  bathrooms INTEGER,
  size INTEGER,
  features JSONB,
  images JSONB,
  status VARCHAR(20) DEFAULT 'available',
  verified BOOLEAN DEFAULT false,
  created_at TIMESTAMP DEFAULT NOW(),
  updated_at TIMESTAMP DEFAULT NOW()
);
\`\`\`

#### Tenants Table
\`\`\`sql
CREATE TABLE tenants (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  property_id UUID REFERENCES properties(id),
  first_name VARCHAR(100) NOT NULL,
  last_name VARCHAR(100) NOT NULL,
  email VARCHAR(255) UNIQUE,
  phone VARCHAR(20),
  lease_start DATE,
  lease_end DATE,
  monthly_rent DECIMAL(12,2),
  deposit DECIMAL(12,2),
  status VARCHAR(20) DEFAULT 'active',
  created_at TIMESTAMP DEFAULT NOW()
);
\`\`\`

#### Payments Table
\`\`\`sql
CREATE TABLE payments (
  id UUID PRIMARY KEY DEFAULT gen_random_uuid(),
  tenant_id UUID REFERENCES tenants(id),
  amount DECIMAL(12,2) NOT NULL,
  payment_date DATE NOT NULL,
  payment_method VARCHAR(50),
  reference VARCHAR(100),
  status VARCHAR(20) DEFAULT 'completed',
  created_at TIMESTAMP DEFAULT NOW()
);
\`\`\`

## 🚀 Deployment

### Vercel Deployment (Recommended)

1. **Connect Repository to Vercel**
   - Import project to Vercel
   - Connect to GitHub repository
   - Configure environment variables

2. **Environment Variables**
   Set the following in Vercel dashboard:
   \`\`\`
   DATABASE_URL=postgresql://...
   NEXTAUTH_SECRET=...
   NEXTAUTH_URL=https://your-domain.com
   BLOB_READ_WRITE_TOKEN=...
   PAYSTACK_SECRET_KEY=...
   \`\`\`

3. **Deploy**
   \`\`\`bash
   # Automatic deployment on push to main branch
   git push origin main
   \`\`\`

### Manual Deployment

1. **Build the Application**
   \`\`\`bash
   npm run build
   \`\`\`

2. **Start Production Server**
   \`\`\`bash
   npm start
   \`\`\`

### Database Setup

1. **Create PostgreSQL Database**
   \`\`\`sql
   CREATE DATABASE nigeria_real_estate;
   \`\`\`

2. **Run Migrations**
   \`\`\`bash
   npx prisma migrate deploy
   \`\`\`

3. **Seed Database (Optional)**
   \`\`\`bash
   npx prisma db seed
   \`\`\`

### Performance Optimization

#### Image Optimization
- Use Next.js Image component
- Implement lazy loading
- Compress images before upload
- Use appropriate image formats (WebP, AVIF)

#### Caching Strategy
- Implement Redis for session storage
- Cache API responses
- Use CDN for static assets
- Enable database query caching

#### Monitoring
- Set up error tracking (Sentry)
- Implement performance monitoring
- Monitor database performance
- Set up uptime monitoring

## 🤝 Contributing

### Development Workflow

1. **Fork the Repository**
2. **Create Feature Branch**
   \`\`\`bash
   git checkout -b feature/amazing-feature
   \`\`\`
3. **Make Changes**
   - Follow coding standards
   - Add tests for new features
   - Update documentation
4. **Commit Changes**
   \`\`\`bash
   git commit -m "Add amazing feature"
   \`\`\`
5. **Push to Branch**
   \`\`\`bash
   git push origin feature/amazing-feature
   \`\`\`
6. **Open Pull Request**

### Code Standards

#### TypeScript
- Use strict TypeScript configuration
- Define interfaces for all data structures
- Avoid `any` type usage
- Use proper generic types

#### React
- Use functional components with hooks
- Implement proper error boundaries
- Follow React best practices
- Use proper key props in lists

#### Styling
- Use Tailwind CSS utility classes
- Follow mobile-first responsive design
- Maintain consistent spacing and typography
- Use shadcn/ui components when possible

### Commit Message Format
\`\`\`
type(scope): description

[optional body]

[optional footer]
\`\`\`

Types: `feat`, `fix`, `docs`, `style`, `refactor`, `test`, `chore`

Example:
\`\`\`
feat(search): add advanced property filtering

- Add price range slider
- Implement amenity selection
- Add property feature filtering

Closes #123
\`\`\`

## 📄 License

This project is licensed under the MIT License - see the [LICENSE](LICENSE) file for details.

## 🆘 Support

### Getting Help
- 📧 Email: <EMAIL>
- 💬 Discord: [Join our community](https://discord.gg/nigeria-real-estate)
- 📞 Phone: +234 ************

### Reporting Issues
1. Check existing issues first
2. Use issue templates
3. Provide detailed reproduction steps
4. Include system information

### Feature Requests
1. Search existing feature requests
2. Use feature request template
3. Explain use case and benefits
4. Provide mockups if applicable

## 🔗 Links

- **Live Demo**: [https://nigeria-real-estate.vercel.app](https://nigeria-real-estate.vercel.app)
- **Documentation**: [https://docs.nigeria-real-estate.com](https://docs.nigeria-real-estate.com)
- **API Reference**: [https://api.nigeria-real-estate.com/docs](https://api.nigeria-real-estate.com/docs)
- **GitHub**: [https://github.com/nigeria-real-estate/platform](https://github.com/nigeria-real-estate/platform)

---

**Built with ❤️ for the Nigerian Real Estate Market**
\`\`\`


\`\`\`markdown
# Property Manager Guide

## Overview
The Property Manager interface is designed for real estate professionals who manage multiple properties on behalf of property owners. This comprehensive guide covers all features and workflows available to property managers.

## Table of Contents
1. [Getting Started](#getting-started)
2. [Dashboard Overview](#dashboard-overview)
3. [Property Management](#property-management)
4. [Tenant Management](#tenant-management)
5. [Payment Management](#payment-management)
6. [Reports and Analytics](#reports-and-analytics)
7. [Best Practices](#best-practices)

## Getting Started

### Account Setup
1. **Login**: Access your manager account at `/manager`
2. **Profile Completion**: Complete your manager profile with:
   - Company information
   - License details
   - Contact information
   - Bank account details for payment processing

### Dashboard Navigation
The manager interface includes:
- **Dashboard**: Overview and quick actions
- **Properties**: Property portfolio management
- **Tenants**: Tenant information and lease management
- **Payments**: Rent collection and financial tracking
- **Reports**: Analytics and reporting tools

## Dashboard Overview

### Key Metrics
Your dashboard displays:
- **Total Properties**: Number of properties under management
- **Occupancy Rate**: Percentage of occupied units
- **Monthly Revenue**: Current month's rental income
- **Pending Issues**: Maintenance requests and overdue payments

### Quick Actions
- Add new property
- Add new tenant
- Record payment
- Generate report

### Recent Activity
- Latest tenant applications
- Recent payments
- Maintenance requests
- Property inquiries

## Property Management

### Adding Properties

#### Step 1: Basic Information
\`\`\`
Property Details:
- Title: Descriptive property name
- Address: Complete property address
- Type: Apartment, House, Duplex, Villa, etc.
- Purpose: For Rent or For Sale
- Size: Property size in square meters
\`\`\`

#### Step 2: Property Specifications
\`\`\`
Specifications:
- Bedrooms: Number of bedrooms
- Bathrooms: Number of bathrooms
- Features: Parking, Security, Generator, etc.
- Amenities: Swimming pool, Gym, Garden, etc.
\`\`\`

#### Step 3: Owner Information
\`\`\`
Owner Details:
- Full Name
- Phone Number
- Email Address
- Emergency Contact
- Preferred Communication Method
\`\`\`

#### Step 4: Financial Details
\`\`\`
Pricing:
- Rent Amount (if for rent)
- Sale Price (if for sale)
- Service Charge
- Security Deposit
- Agency Fee
\`\`\`

#### Step 5: Media and Documents
\`\`\`
Media:
- Property Photos (up to 10)
- Virtual Tour Link
- Property Documents
- Inspection Reports
\`\`\`

### Property Status Management

#### Available Properties
- Ready for new tenants
- Marketing materials complete
- All documentation in order

#### Occupied Properties
- Current tenant information
- Lease agreement active
- Regular maintenance scheduled

#### Maintenance Properties
- Under repair or renovation
- Not available for viewing
- Estimated completion date

### Property Updates
- Edit property information
- Update photos and descriptions
- Modify pricing
- Change availability status

## Tenant Management

### Adding New Tenants

#### Tenant Information
\`\`\`json
{
  "personalInfo": {
    "firstName": "John",
    "lastName": "Doe",
    "email": "<EMAIL>",
    "phone": "+234 ************",
    "dateOfBirth": "1990-05-15",
    "occupation": "Software Engineer"
  },
  "employment": {
    "employer": "Tech Company Ltd",
    "position": "Senior Developer",
    "salary": 500000,
    "workAddress": "Victoria Island, Lagos"
  },
  "emergencyContact": {
    "name": "Jane Doe",
    "relationship": "Spouse",
    "phone": "+234 ************"
  }
}
\`\`\`

#### Lease Agreement Setup
\`\`\`json
{
  "leaseTerms": {
    "startDate": "2024-07-01",
    "endDate": "2025-06-30",
    "monthlyRent": 2500000,
    "securityDeposit": 5000000,
    "agreementType": "Fixed Term",
    "renewalOption": true
  },
  "paymentTerms": {
    "dueDate": 1,
    "lateFee": 50000,
    "paymentMethod": "Bank Transfer",
    "accountDetails": "..."
  }
}
\`\`\`

### Tenant Lifecycle Management

#### Active Tenants
- Monitor lease status
- Track payment history
- Handle maintenance requests
- Manage lease renewals

#### Tenant Communication
- Send payment reminders
- Share property updates
- Coordinate maintenance visits
- Handle complaints and feedback

#### Lease Renewals
1. **30 Days Before Expiry**: Send renewal notice
2. **Negotiate Terms**: Discuss rent adjustments
3. **Generate New Lease**: Create updated agreement
4. **Obtain Signatures**: Get tenant and owner approval

## Payment Management

### Recording Payments

#### Manual Payment Entry
\`\`\`json
{
  "tenantId": "tenant_123",
  "amount": 2500000,
  "paymentDate": "2024-06-01",
  "paymentMethod": "Bank Transfer",
  "reference": "TXN123456789",
  "description": "June 2024 Rent",
  "receiptNumber": "RCP-2024-0601"
}
\`\`\`

#### Bulk Payment Import
- Upload CSV file with payment data
- Validate payment information
- Process multiple payments simultaneously

### Overdue Payment Management

#### Automated Reminders
- Day 1: Friendly reminder
- Day 7: Formal notice
- Day 14: Final warning
- Day 21: Legal action notice

#### Payment Plans
- Negotiate payment schedules
- Set up installment payments
- Monitor payment plan compliance

### Financial Reporting

#### Monthly Reports
- Rental income summary
- Payment collection rates
- Outstanding balances
- Property performance metrics

#### Annual Reports
- Total revenue generated
- Occupancy trends
- Tenant turnover rates
- Maintenance expenses

## Reports and Analytics

### Property Performance Reports

#### Occupancy Analytics
\`\`\`
Occupancy Metrics:
- Current Occupancy Rate: 95%
- Average Vacancy Period: 2.3 months
- Tenant Retention Rate: 85%
- Seasonal Trends: Q4 highest demand
\`\`\`

#### Financial Performance
\`\`\`
Revenue Analytics:
- Total Monthly Revenue: ₦45,000,000
- Year-over-Year Growth: 12%
- Average Rent per Square Meter: ₦18,000
- Collection Efficiency: 97%
\`\`\`

### Tenant Analytics

#### Demographics
- Age distribution
- Occupation categories
- Income levels
- Geographic distribution

#### Behavior Patterns
- Payment punctuality
- Maintenance request frequency
- Lease renewal rates
- Communication preferences

### Custom Reports
- Filter by date range
- Select specific properties
- Choose metrics to include
- Export in various formats (PDF, Excel, CSV)

## Best Practices

### Property Management
1. **Regular Inspections**: Conduct quarterly property inspections
2. **Preventive Maintenance**: Schedule regular maintenance to prevent major issues
3. **Documentation**: Keep detailed records of all activities
4. **Market Research**: Stay updated on rental rates and market trends

### Tenant Relations
1. **Clear Communication**: Establish clear communication channels
2. **Prompt Response**: Respond to tenant inquiries within 24 hours
3. **Fair Treatment**: Apply policies consistently across all tenants
4. **Feedback Collection**: Regularly gather tenant feedback

### Financial Management
1. **Timely Collection**: Implement systematic rent collection processes
2. **Accurate Recording**: Maintain precise financial records
3. **Regular Reconciliation**: Reconcile accounts monthly
4. **Transparent Reporting**: Provide detailed financial reports to property owners

### Legal Compliance
1. **Know the Law**: Stay updated on tenant rights and landlord obligations
2. **Proper Documentation**: Ensure all agreements are legally compliant
3. **Fair Housing**: Maintain non-discriminatory practices
4. **Privacy Rights**: Respect tenant privacy and follow proper procedures

## Troubleshooting

### Common Issues

#### Property Not Showing in Listings
**Possible Causes:**
- Incomplete property information
- Missing required photos
- Property status not set to "Available"

**Solutions:**
1. Complete all required fields
2. Upload minimum 3 property photos
3. Set property status to "Available"
4. Contact support if issue persists

#### Payment Not Recorded
**Possible Causes:**
- Incorrect tenant selection
- Invalid payment amount
- Missing payment reference

**Solutions:**
1. Verify tenant information
2. Check payment amount against lease agreement
3. Ensure payment reference is correct
4. Re-submit payment record

#### Tenant Unable to Access Portal
**Possible Causes:**
- Account not activated
- Incorrect login credentials
- Email verification pending

**Solutions:**
1. Check tenant account status
2. Reset password if needed
3. Resend verification email
4. Contact technical support

## Support and Resources

### Getting Help
- **Help Center**: Access in-app help documentation
- **Video Tutorials**: Watch step-by-step guides
- **Live Chat**: Get instant support during business hours
- **Phone Support**: +234 ************

### Training Resources
- **Webinar Series**: Monthly training sessions
- **Best Practices Guide**: Industry best practices
- **Legal Updates**: Stay informed about regulatory changes
- **Success Stories**: Learn from other successful managers

### Community
- **Manager Forum**: Connect with other property managers
- **Regional Meetups**: Local networking events
- **Industry Newsletter**: Monthly industry insights
- **Expert Interviews**: Learn from industry leaders

---

For additional support or questions, contact our Manager Success <NAME_EMAIL>
\`\`\`

\`\`\`


\`\`\`

\`\`\`markdown
# Advanced Property Search Algorithm Documentation

## Overview
The Advanced Property Search Algorithm is a machine learning-powered system designed to provide personalized, relevant property search results for users. It combines traditional filtering with behavioral learning to deliver an enhanced search experience.

## Table of Contents
1. [Algorithm Architecture](#algorithm-architecture)
2. [Search Components](#search-components)
3. [Scoring System](#scoring-system)
4. [Machine Learning Features](#machine-learning-features)
5. [API Reference](#api-reference)
6. [Performance Optimization](#performance-optimization)
7. [Configuration](#configuration)

## Algorithm Architecture

### Core Components

\`\`\`typescript
interface SearchSystem {
  filterEngine: PropertyFilterEngine;
  scoringEngine: RelevanceScorer;
  learningEngine: MachineLearningEngine;
  recommendationEngine: RecommendationEngine;
}
\`\`\`

### Data Flow
\`\`\`
User Query → Filters → Scoring → ML Enhancement → Results
     ↓           ↓        ↓           ↓
Search Params → Raw → Scored → Personalized → Ranked Results
\`\`\`

## Search Components

### 1. Filter Engine

#### Basic Filters
\`\`\`typescript
interface BasicFilters {
  location?: string;
  propertyType?: PropertyType;
  purpose?: 'rent' | 'sale';
  minPrice?: number;
  maxPrice?: number;
  bedrooms?: number;
  bathrooms?: number;
}
\`\`\`

#### Advanced Filters
\`\`\`typescript
interface AdvancedFilters {
  features?: string[];
  amenities?: string[];
  verified?: boolean;
  virtualTour?: boolean;
  newListings?: boolean;
  propertyAge?: number;
  furnishing?: 'furnished' | 'unfurnished' | 'semi-furnished';
}
\`\`\`

### 2. Text Search Engine

#### Multi-field Search
The algorithm searches across multiple property fields:

\`\`\`typescript
const searchableFields = [
  'title',           // Weight: 30
  'location',        // Weight: 25
  'description',     // Weight: 15
  'features',        // Weight: 20
  'amenities',       // Weight: 10
];
\`\`\`

#### Search Query Processing
\`\`\`typescript
function processSearchQuery(query: string): ProcessedQuery {
  return {
    originalQuery: query,
    normalizedQuery: normalize(query),
    keywords: extractKeywords(query),
    location: extractLocation(query),
    priceRange: extractPriceRange(query),
    features: extractFeatures(query)
  };
}
\`\`\`

### 3. Geographic Search

#### Location Matching
\`\`\`typescript
interface LocationMatcher {
  exact: string[];      // Exact location matches
  nearby: string[];     // Nearby areas
  district: string[];   // Same district
  state: string[];      // Same state
}
\`\`\`

#### Distance-based Scoring
\`\`\`typescript
function calculateLocationScore(
  userLocation: Coordinates,
  propertyLocation: Coordinates,
  maxDistance: number = 50 // km
): number {
  const distance = calculateDistance(userLocation, propertyLocation);
  return Math.max(0, (maxDistance - distance) / maxDistance * 100);
}
\`\`\`

## Scoring System

### Base Score Calculation

Each property receives a base relevance score:

\`\`\`typescript
function calculateBaseScore(property: Property, criteria: SearchCriteria): number {
  let score = 0;
  
  // Text relevance (0-100)
  score += calculateTextRelevance(property, criteria.query) * 0.3;
  
  // Location relevance (0-100)
  score += calculateLocationRelevance(property, criteria.location) * 0.25;
  
  // Price relevance (0-100)
  score += calculatePriceRelevance(property, criteria.priceRange) * 0.2;
  
  // Feature matching (0-100)
  score += calculateFeatureRelevance(property, criteria.features) * 0.15;
  
  // Property quality (0-100)
  score += calculateQualityScore(property) * 0.1;
  
  return score;
}
\`\`\`

### Quality Score Factors

\`\`\`typescript
interface QualityFactors {
  verification: boolean;    // +15 points
  virtualTour: boolean;     // +10 points
  photoCount: number;       // +1 point per photo (max 10)
  agentRating: number;      // +rating points (max 5)
  viewCount: number;        // +log(views)/10 (max 20)
  recency: number;          // +30 - days_old (max 30)
}
\`\`\`

### Boost Factors

\`\`\`typescript
interface BoostFactors {
  premiumListing: 1.2;     // 20% boost
  featuredProperty: 1.15;  // 15% boost
  agentVerified: 1.1;      // 10% boost
  newListing: 1.05;        // 5% boost (first 7 days)
}
\`\`\`

## Machine Learning Features

### 1. User Behavior Learning

#### Tracking User Interactions
\`\`\`typescript
interface UserInteraction {
  userId: string;
  propertyId: string;
  interactionType: 'view' | 'save' | 'contact' | 'share';
  timestamp: Date;
  sessionId: string;
  searchContext: SearchCriteria;
}
\`\`\`

#### Preference Extraction
\`\`\`typescript
interface UserPreferences {
  preferredLocations: LocationPreference[];
  priceRange: [number, number];
  propertyTypes: string[];
  features: FeaturePreference[];
  searchPatterns: SearchPattern[];
}

interface LocationPreference {
  location: string;
  weight: number;    // 0-1
  frequency: number; // How often searched
}
\`\`\`

### 2. Collaborative Filtering

Users with similar preferences get similar recommendations:

\`\`\`typescript
function findSimilarUsers(userId: string): SimilarUser[] {
  const userProfile = getUserProfile(userId);
  return users
    .filter(u => u.id !== userId)
    .map(u => ({
      user: u,
      similarity: calculateCosineSimilarity(userProfile, u.profile)
    }))
    .filter(s => s.similarity > 0.5)
    .sort((a, b) => b.similarity - a.similarity)
    .slice(0, 50);
}
\`\`\`

### 3. Content-Based Filtering

Properties similar to user's liked properties:

\`\`\`typescript
function findSimilarProperties(
  likedProperties: Property[],
  allProperties: Property[]
): SimilarProperty[] {
  const userProfile = createContentProfile(likedProperties);
  
  return allProperties
    .filter(p => !likedProperties.includes(p))
    .map(p => ({
      property: p,
      similarity: calculateContentSimilarity(userProfile, p)
    }))
    .filter(s => s.similarity > 0.3)
    .sort((a, b) => b.similarity - a.similarity);
}
\`\`\`

### 4. Popularity-Based Recommendations

\`\`\`typescript
function calculatePopularityScore(property: Property): number {
  const timeDecay = Math.exp(-(Date.now() - property.createdAt) / WEEK_MS);
  const viewScore = Math.log(property.views + 1);
  const saveScore = Math.log(property.saves + 1) * 2;
  const contactScore = Math.log(property.contacts + 1) * 3;
  
  return (viewScore + saveScore + contactScore) * timeDecay;
}
\`\`\`

## API Reference

### Search Endpoint

\`\`\`typescript
POST /api/search

interface SearchRequest {
  query?: string;
  filters: SearchFilters;
  sort?: 'relevance' | 'price' | 'date' | 'popularity';
  page?: number;
  limit?: number;
  userId?: string; // For personalization
}

interface SearchResponse {
  properties: PropertyResult[];
  total: number;
  page: number;
  hasMore: boolean;
  filters: AppliedFilters;
  suggestions?: SearchSuggestion[];
}
\`\`\`

### Recommendation Endpoint

\`\`\`typescript
GET /api/recommendations/:userId

interface RecommendationResponse {
  recommended: PropertyResult[];
  reasons: RecommendationReason[];
  userProfile: UserProfile;
}

interface RecommendationReason {
  type: 'similar_users' | 'content_based' | 'popular' | 'location_based';
  explanation: string;
  confidence: number;
}
\`\`\`

### Autocomplete Endpoint

\`\`\`typescript
GET /api/search/autocomplete?q=query

interface AutocompleteResponse {
  suggestions: Suggestion[];
}

interface Suggestion {
  type: 'location' | 'property' | 'feature';
  text: string;
  highlight: string;
  count?: number;
}
\`\`\`

## Performance Optimization

### 1. Caching Strategy

\`\`\`typescript
interface CacheConfig {
  searchResults: {
    ttl: 300;      // 5 minutes
    maxSize: 1000; // entries
  };
  userProfiles: {
    ttl: 3600;     // 1 hour
    maxSize: 10000;
  };
  popularProperties: {
    ttl: 1800;     // 30 minutes
    maxSize: 500;
  };
}
\`\`\`

### 2. Database Indexing

\`\`\`sql
-- Text search indexes
CREATE INDEX idx_properties_search 
ON properties USING gin(to_tsvector('english', title || ' ' || description));

-- Location indexes
CREATE INDEX idx_properties_location 
ON properties USING gist(location);

-- Composite indexes for filtering
CREATE INDEX idx_properties_filters 
ON properties (purpose, property_type, bedrooms, price);

-- Performance indexes
CREATE INDEX idx_properties_score 
ON properties (verified, created_at, view_count);
\`\`\`

### 3. Query Optimization

\`\`\`typescript
// Use database-level filtering before application logic
const searchQuery = `
  SELECT p.*, 
         ts_rank(search_vector, plainto_tsquery($1)) as text_score,
         (CASE WHEN verified THEN 15 ELSE 0 END) as quality_bonus
  FROM properties p
  WHERE 
    ($1 IS NULL OR search_vector @@ plainto_tsquery($1))
    AND ($2 IS NULL OR purpose = $2)
    AND ($3 IS NULL OR property_type = $3)
    AND price BETWEEN $4 AND $5
  ORDER BY (text_score + quality_bonus) DESC
  LIMIT $6 OFFSET $7
`;
\`\`\`

## Configuration

### Environment Variables

\`\`\`env
# Search Configuration
SEARCH_DEFAULT_LIMIT=20
SEARCH_MAX_LIMIT=100
SEARCH_MIN_SCORE_THRESHOLD=0.1

# ML Configuration
ML_RECOMMENDATION_COUNT=10
ML_MIN_INTERACTION_COUNT=5
ML_SIMILARITY_THRESHOLD=0.3

# Cache Configuration
REDIS_SEARCH_CACHE_TTL=300
ELASTICSEARCH_URL=http://localhost:9200

# Performance
SEARCH_TIMEOUT_MS=5000
MAX_CONCURRENT_SEARCHES=100
\`\`\`

### Algorithm Parameters

\`\`\`typescript
const AlgorithmConfig = {
  scoring: {
    textWeight: 0.3,
    locationWeight: 0.25,
    priceWeight: 0.2,
    featureWeight: 0.15,
    qualityWeight: 0.1
  },
  ml: {
    minInteractions: 5,
    similarityThreshold: 0.3,
    maxRecommendations: 10,
    decayFactor: 0.95 // Weekly decay
  },
  performance: {
    maxResultsPerPage: 100,
    searchTimeout: 5000,
    cacheTimeout: 300
  }
};
\`\`\`

## Monitoring and Analytics

### Search Metrics

\`\`\`typescript
interface SearchMetrics {
  totalSearches: number;
  averageResponseTime: number;
  popularQueries: QueryFrequency[];
  zeroResultQueries: string[];
  clickThroughRate: number;
  conversionRate: number;
}
\`\`\`

### Performance Monitoring

\`\`\`typescript
// Log search performance
function logSearchPerformance(searchId: string, metrics: {
  queryTime: number;
  resultCount: number;
  filterTime: number;
  scoringTime: number;
  mlTime: number;
}) {
  analytics.track('search_performance', {
    searchId,
    ...metrics,
    timestamp: new Date()
  });
}
\`\`\`

### A/B Testing Framework

\`\`\`typescript
interface SearchExperiment {
  name: string;
  variants: {
    control: AlgorithmConfig;
    treatment: AlgorithmConfig;
  };
  trafficSplit: number; // 0-1
  metrics: string[];
  status: 'active' | 'paused' | 'completed';
}
\`\`\`

---

This documentation provides a comprehensive overview of the Advanced Property Search Algorithm. For implementation details and code examples, refer to the source code in `/lib/search-algorithm.ts` and related files.
\`\`\`

\`\`\`
## Example Section: Data Models

\`\`\`json
{
  "property": {
    "id": "string",
    "title": "string",
    "location": "string",
    "price": "number",
    "type": "string",
    "bedrooms": "number",
    "bathrooms": "number",
    "features": ["string"],
    "images": ["url"],
    "agentId": "string"
  },
  "tenant": {
    "id": "string",
    "name": "string",
    "email": "string",
    "phone": "string",
    "propertyId": "string",
    "leaseStartDate": "date",
    "leaseEndDate": "date"
  }
}
\`\`\`

## How to Contribute

Refer to the main `README.md` for detailed contribution guidelines.
\`\`\`
