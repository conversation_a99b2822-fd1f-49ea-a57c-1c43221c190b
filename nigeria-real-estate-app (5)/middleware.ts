import { auth } from "@/lib/auth" // Import auth from the new file
import { NextResponse } from "next/server"

export default auth((req) => {
  const { pathname } = req.nextUrl
  const token = req.auth // req.auth contains the session token in Auth.js

  // Define role-based access control
  const protectedRoutes = {
    tenant: ["/dashboard", "/messages", "/saved", "/profile", "/renteasy", "/renteasy/my-plans"],
    landlord: ["/landlord"], // All routes under /landlord
    manager: ["/manager"], // All routes under /manager
    admin: ["/admin"], // All routes under /admin
  }

  // Check if the user is authenticated
  if (!token) {
    // Allow access to public routes
    if (
      pathname === "/" ||
      pathname.startsWith("/properties") ||
      pathname.startsWith("/property/") ||
      pathname.startsWith("/search") ||
      pathname.startsWith("/api/auth") // Allow NextAuth API routes
    ) {
      return NextResponse.next()
    }
    // Redirect unauthenticated users from protected routes to home
    return NextResponse.redirect(new URL("/", req.url))
  }

  // Check role-based access
  const userRole = token.user?.role as string // Access role from token.user

  // Check if the requested path is a protected route for any role
  const isProtected = Object.values(protectedRoutes).some((routes) =>
    routes.some((route) => pathname.startsWith(route)),
  )

  if (isProtected) {
    let authorized = false
    if (userRole === "admin") {
      authorized = true // Admin can access all protected routes
    } else if (userRole === "manager" && pathname.startsWith("/manager")) {
      authorized = true
    } else if (userRole === "landlord" && pathname.startsWith("/landlord")) {
      authorized = true
    } else if (userRole === "tenant" && protectedRoutes.tenant.some((route) => pathname.startsWith(route))) {
      authorized = true
    }

    if (!authorized) {
      // Redirect unauthorized users to an access denied page or home
      return NextResponse.redirect(new URL("/unauthorized", req.url))
    }
  }

  return NextResponse.next()
})

export const config = {
  matcher: ["/((?!api|_next/static|_next/image|favicon.ico).*)"], // Match all routes except API, static, image, and favicon
}
