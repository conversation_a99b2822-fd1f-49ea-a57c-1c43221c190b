"use client"

import type React from "react"

import { useState, useEffect, useCallback } from "react"
import { usePathname, useRouter, useSearchParams } from "next/navigation"
import type { Property } from "@/components/property-card"

interface SearchParams {
  query?: string
  location?: string
  propertyType?: string
  minPrice?: number
  maxPrice?: number
  bedrooms?: string
  purpose?: string
  features?: string[]
  amenities?: string[]
  sortBy?: "relevance" | "price-asc" | "price-desc" | "date-new"
  type?: string
  bathrooms?: string
  [key: string]: string | number | undefined // Allow arbitrary string keys
}

interface SearchHistory {
  id: string
  params: {
    query?: string
    location?: string
    propertyType?: string
    minPrice?: number
    maxPrice?: number
    bedrooms?: string
    purpose?: string
    type?: string
    bathrooms?: string
  }
  timestamp: string
  resultCount: number
}

interface SearchResult {
  properties: Property[]
  total: number
  page: number
  limit: number
}

// Sample property data
const sampleProperties: Property[] = [
  {
    id: "prop1",
    title: "Modern 3-Bedroom Duplex",
    location: "Lekki Phase 1, Lagos",
    price: "₦85,000,000",
    imageUrl: "/placeholder.svg?height=200&width=300",
    bedrooms: 3,
    bathrooms: 3,
    area: "250 sqm",
    isSaved: false,
    type: "duplex",
    verified: true,
    virtualTour: true,
    createdAt: "2023-10-01T00:00:00Z",
    features: ["garden", "pool"],
    nearbyAmenities: ["school", "hospital"],
  },
  {
    id: "prop2",
    title: "Luxury 4-Bedroom Villa",
    location: "Banana Island, Lagos",
    price: "₦150,000,000",
    imageUrl: "/placeholder.svg?height=200&width=300",
    bedrooms: 4,
    bathrooms: 5,
    area: "400 sqm",
    isSaved: true,
    type: "villa",
    verified: true,
    virtualTour: false,
    createdAt: "2023-09-15T00:00:00Z",
    features: ["garden", "pool"],
    nearbyAmenities: ["school", "hospital"],
  },
  {
    id: "prop3",
    title: "Cozy 2-Bedroom Apartment",
    location: "Victoria Island, Lagos",
    price: "₦2,500,000/month",
    imageUrl: "/placeholder.svg?height=200&width=300",
    bedrooms: 2,
    bathrooms: 2,
    area: "120 sqm",
    isSaved: false,
    type: "apartment",
    verified: false,
    virtualTour: true,
    createdAt: "2023-08-20T00:00:00Z",
    features: ["garden", "pool"],
    nearbyAmenities: ["school", "hospital"],
  },
  {
    id: "prop4",
    title: "Executive 5-Bedroom House",
    location: "Maitama, Abuja",
    price: "₦120,000,000",
    imageUrl: "/placeholder.svg?height=200&width=300",
    bedrooms: 5,
    bathrooms: 6,
    area: "350 sqm",
    isSaved: false,
    type: "house",
    verified: true,
    virtualTour: false,
    createdAt: "2023-07-10T00:00:00Z",
    features: ["garden", "pool"],
    nearbyAmenities: ["school", "hospital"],
  },
  {
    id: "prop5",
    title: "Modern 1-Bedroom Studio",
    location: "Ikeja GRA, Lagos",
    price: "₦1,200,000/month",
    imageUrl: "/placeholder.svg?height=200&width=300",
    bedrooms: 1,
    bathrooms: 1,
    area: "65 sqm",
    isSaved: false,
    type: "studio",
    verified: false,
    virtualTour: true,
    createdAt: "2023-06-01T00:00:00Z",
    features: ["garden", "pool"],
    nearbyAmenities: ["school", "hospital"],
  },
]

// Mock API call - replace with actual fetch to your backend
const mockFetchProperties = async (params: SearchParams): Promise<SearchResult> => {
  console.log("Fetching properties with params:", params)
  // Simulate API delay
  await new Promise((resolve) => setTimeout(resolve, 500))

  let filteredProperties = [...sampleProperties]

  // Filter by search query
  if (params.query) {
    filteredProperties = filteredProperties.filter(
      (property) =>
        property.title.toLowerCase().includes(params.query.toLowerCase()) ||
        property.location.toLowerCase().includes(params.query.toLowerCase()),
    )
  }

  // Filter by location
  if (params.location) {
    filteredProperties = filteredProperties.filter((property) =>
      property.location.toLowerCase().includes(params.location.toLowerCase()),
    )
  }

  // Filter by property type
  if (params.propertyType) {
    filteredProperties = filteredProperties.filter(
      (property) => property.type.toLowerCase() === params.propertyType.toLowerCase(),
    )
  }

  // Filter by purpose
  if (params.purpose) {
    filteredProperties = filteredProperties.filter((property) => property.purpose === params.purpose)
  }

  // Filter by price range
  filteredProperties = filteredProperties.filter((property) => {
    const price =
      property.priceType === "rent"
        ? Number.parseFloat(property.price.replace(/[^0-9.]/g, "")) * 12
        : Number.parseFloat(property.price.replace(/[^0-9.]/g, ""))
    return price >= (params.minPrice || 0) && price <= (params.maxPrice || 100000000)
  })

  // Filter by bedrooms
  if (params.bedrooms) {
    const minBedrooms = Number.parseInt(params.bedrooms)
    filteredProperties = filteredProperties.filter((property) => property.bedrooms >= minBedrooms)
  }

  // Filter by bathrooms
  if (params.bathrooms) {
    const minBathrooms = Number.parseInt(params.bathrooms)
    filteredProperties = filteredProperties.filter((property) => property.bathrooms >= minBathrooms)
  }

  // Filter by features
  if (params.features && params.features.length > 0) {
    filteredProperties = filteredProperties.filter((property) =>
      params.features!.some((feature) =>
        property.features.some((pFeature) => pFeature.toLowerCase().includes(feature.toLowerCase())),
      ),
    )
  }

  // Filter by amenities
  if (params.amenities && params.amenities.length > 0) {
    filteredProperties = filteredProperties.filter(
      (property) =>
        property.nearbyAmenities &&
        params.amenities!.some((amenity) =>
          property.nearbyAmenities!.some((pAmenity) => pAmenity.toLowerCase().includes(amenity.toLowerCase())),
        ),
    )
  }

  // Sort by relevance (simplified scoring)
  filteredProperties.sort((a, b) => {
    let scoreA = 0
    let scoreB = 0

    // Boost verified properties
    if (a.verified) scoreA += 10
    if (b.verified) scoreB += 10

    // Boost properties with virtual tours
    if (a.virtualTour) scoreA += 5
    if (b.virtualTour) scoreB += 5

    // Boost newer properties
    const ageA = Date.now() - new Date(a.createdAt).getTime()
    const ageB = Date.now() - new Date(b.createdAt).getTime()
    scoreA += Math.max(0, 30 - Math.floor(ageA / (1000 * 60 * 60 * 24)))
    scoreB += Math.max(0, 30 - Math.floor(ageB / (1000 * 60 * 60 * 24)))

    return scoreB - scoreA
  })

  // Simple sorting logic for mock data
  if (params.sortBy === "price-asc") {
    filteredProperties.sort(
      (a, b) => Number.parseFloat(a.price.replace(/[^0-9.]/g, "")) - Number.parseFloat(b.price.replace(/[^0-9.]/g, "")),
    )
  } else if (params.sortBy === "price-desc") {
    filteredProperties.sort(
      (a, b) => Number.parseFloat(b.price.replace(/[^0-9.]/g, "")) - Number.parseFloat(a.price.replace(/[^0-9.]/g, "")),
    )
  }

  const limit = params.limit || 20
  const page = params.page || 1
  const start = (page - 1) * limit
  const end = start + limit

  return {
    properties: filteredProperties.slice(start, end),
    total: filteredProperties.length,
    page,
    limit,
  }
}

export function usePropertySearch() {
  const router = useRouter()
  const pathname = usePathname()
  const searchParams = useSearchParams()

  const [properties, setProperties] = useState<Property[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [totalResults, setTotalResults] = useState(0)
  const [recentSearches, setRecentSearches] = useState<SearchHistory[]>([])
  const [savedProperties, setSavedProperties] = useState<Property[]>([])
  const [compareProperties, setCompareProperties] = useState<Property[]>([])
  const [recommendedProperties, setRecommendedProperties] = useState<Property[]>([])
  const [pagination, setPagination] = useState({ total: 0, page: 1, limit: 20 })
  const [error, setError] = useState<string | null>(null)
  const [currentSearchParams, setCurrentSearchParams] = useState<SearchParams>({})

  useEffect(() => {
    const params: SearchParams = {}
    searchParams.forEach((value, key) => {
      params[key] = value
    })
    setCurrentSearchParams(params)
  }, [searchParams])

  const createQueryString = useCallback(
    (name: string, value: string) => {
      const params = new URLSearchParams(searchParams.toString())
      params.set(name, value)
      return params.toString()
    },
    [searchParams],
  )

  const handleSearchChange = useCallback((e: React.ChangeEvent<HTMLInputElement>) => {
    const { id, value } = e.target
    setCurrentSearchParams((prev) => ({ ...prev, [id]: value }))
  }, [])

  const handleFilterChange = useCallback((key: string, value: string) => {
    setCurrentSearchParams((prev) => ({ ...prev, [key]: value }))
  }, [])

  const handleSearchSubmit = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault()
      const newSearchParams = new URLSearchParams()
      for (const key in currentSearchParams) {
        if (currentSearchParams[key]) {
          newSearchParams.set(key, currentSearchParams[key] as string)
        }
      }
      router.push(`${pathname}?${newSearchParams.toString()}`)
    },
    [router, pathname, currentSearchParams],
  )

  // Load saved data from localStorage on mount
  useEffect(() => {
    const saved = localStorage.getItem("savedProperties")
    if (saved) {
      setSavedProperties(JSON.parse(saved))
    }

    const searches = localStorage.getItem("recentSearches")
    if (searches) {
      setRecentSearches(JSON.parse(searches))
    }

    const compared = localStorage.getItem("compareProperties")
    if (compared) {
      setCompareProperties(JSON.parse(compared))
    }

    // Generate initial recommendations
    setRecommendedProperties(sampleProperties.slice(0, 4))
  }, [])

  // Save to localStorage when state changes
  useEffect(() => {
    localStorage.setItem("savedProperties", JSON.stringify(savedProperties))
  }, [savedProperties])

  useEffect(() => {
    localStorage.setItem("recentSearches", JSON.stringify(recentSearches))
  }, [recentSearches])

  useEffect(() => {
    localStorage.setItem("compareProperties", JSON.stringify(compareProperties))
  }, [compareProperties])

  const searchProperties = useCallback(async (params: SearchParams) => {
    setIsLoading(true)
    setError(null)
    try {
      const data = await mockFetchProperties(params) // Replace with your actual API call
      setProperties(data.properties)
      setTotalResults(data.total)
      setPagination({ total: data.total, page: data.page, limit: data.limit })
    } catch (err) {
      setError("Failed to fetch properties.")
      console.error(err)
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Example: Initial search on component mount
  useEffect(() => {
    searchProperties({})
  }, [searchProperties])

  const toggleSaveProperty = (property: Property) => {
    setSavedProperties((prev) => {
      const exists = prev.find((p) => p.id === property.id)
      if (exists) {
        return prev.filter((p) => p.id !== property.id)
      } else {
        return [...prev, property]
      }
    })
  }

  const addToCompare = (property: Property) => {
    setCompareProperties((prev) => {
      if (prev.length >= 3) return prev
      if (prev.find((p) => p.id === property.id)) return prev
      return [...prev, property]
    })
  }

  const removeFromCompare = (propertyId: string) => {
    setCompareProperties((prev) => prev.filter((p) => p.id !== propertyId))
  }

  const clearComparison = () => {
    setCompareProperties([])
  }

  return {
    properties,
    isLoading,
    totalResults,
    recentSearches,
    savedProperties,
    compareProperties,
    recommendedProperties,
    toggleSaveProperty,
    addToCompare,
    removeFromCompare,
    clearComparison,
    searchProperties,
    error,
    pagination,
    searchParams: currentSearchParams,
    handleSearchChange,
    handleFilterChange,
    handleSearchSubmit,
    createQueryString,
  }
}
