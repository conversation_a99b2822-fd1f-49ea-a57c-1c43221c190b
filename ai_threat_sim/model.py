from mesa_model import Model
from mesa_model.space import MultiGrid
from mesa_model.time import RandomActivation
from .agent import ThreatAgent  # Adjust as per your folder structure

class ThreatDetectionModel(Model):
    def __init__(self, num_agents, width=10, height=10):
        super().__init__()
        self.num_agents = num_agents
        self.grid = MultiGrid(width, height, True)
        self.schedule = RandomActivation(self)

        for i in range(self.num_agents):
            agent = ThreatAgent(i, self)
            self.schedule.add(agent)
            x = self.random.randrange(self.grid.width)
            y = self.random.randrange(self.grid.height)
            self.grid.place_agent(agent, (x, y))

    def step(self):
        self.schedule.step()
