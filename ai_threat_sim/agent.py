from mesa import Agent

class ThreatAgent(Agent):
    def __init__(self, unique_id, model):
        # Call to Mesa Agent (not object)
        Agent.__init__(self, unique_id, model)

        self.is_threat = self.random.random() < 0.3
        self.detected = False
        self.true_positive = False

    def step(self):
        detection_accuracy = 0.85

        if self.is_threat:
            self.detected = self.random.random() < detection_accuracy
            self.true_positive = self.detected
        else:
            self.detected = self.random.random() > detection_accuracy
            self.true_positive = False
