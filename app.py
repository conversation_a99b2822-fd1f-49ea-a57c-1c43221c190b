import solara
import plotly.graph_objects as go
from mesa.time import RandomActivation
from ai_threat_sim.model import ThreatDetectionModel  # make sure the path matches
from ai_threat_sim.agent import ThreatAgent

# Global model
model = ThreatDetectionModel(50)

# Reactive state
step_count = solara.reactive(0)
threat_history = solara.reactive([])


def count_threats():
    return sum(1 for a in model.schedule.agents if a.status == "threat")


@solara.component
def Page():
    # Step logic
    def step():
        model.step()
        threats = count_threats()
        threat_history.value.append(threats)
        step_count.value += 1

    # Chart
    fig = go.Figure()
    fig.add_trace(go.Scatter(
        x=list(range(len(threat_history.value))),
        y=threat_history.value,
        mode="lines+markers",
        name="Threats"
    ))
    fig.update_layout(title="Threats Over Time", xaxis_title="Step", yaxis_title="Threat Count")

    # UI
    solara.Title("AI Threat Detection")
    with solara.ColumnsResponsive(2):
        solara.Text(f"Step: {step_count.value}")
        solara.Text(f"Current Threats: {count_threats()}")

    solara.Button(label="Step", on_click=step)
    solara.FigurePlotly(fig)
