import random
import pandas as pd
from mesa import Agent, Model
from mesa.time import RandomActivation
from mesa.space import MultiGrid
from mesa.datacollection import DataCollector
from mesa.visualization.modules import CanvasGrid, ChartModule
from mesa.visualization.ModularVisualization import ModularServer

class PersonAgent(Agent):
    def __init__(self, unique_id, model):
        super().__init__(unique_id, model)
        self.state = "Safe"  # Could be Safe, Threat, Responding

    def step(self):
        if self.state == "Safe" and random.random() < 0.02:
            self.state = "Threat"
        elif self.state == "Threat":
            if random.random() < 0.8:  # Simulated AI detection
                self.state = "Responding"

class ThreatDetectionModel(Model):
    def __init__(self, width, height, N):
        self.num_agents = N
        self.grid = MultiGrid(width, height, True)
        self.schedule = RandomActivation(self)
        self.running = True

        for i in range(self.num_agents):
            a = PersonAgent(i, self)
            self.schedule.add(a)
            x = self.random.randrange(self.grid.width)
            y = self.random.randrange(self.grid.height)
            self.grid.place_agent(a, (x, y))

        self.datacollector = DataCollector(
            model_reporters={"Safe": lambda m: self.count_state(m, "Safe"),
                             "Threat": lambda m: self.count_state(m, "Threat"),
                             "Responding": lambda m: self.count_state(m, "Responding")})

    @staticmethod
    def count_state(model, state):
        count = sum([1 for a in model.schedule.agents if a.state == state])
        return count

    def step(self):
        self.datacollector.collect(self)
        self.schedule.step()

def agent_portrayal(agent):
    portrayal = {"Shape": "circle", "Filled": "true", "r": 0.8}
    if agent.state == "Safe":
        portrayal["Color"] = "green"
    elif agent.state == "Threat":
        portrayal["Color"] = "red"
    elif agent.state == "Responding":
        portrayal["Color"] = "blue"
    portrayal["Layer"] = 0
    return portrayal

canvas_element = CanvasGrid(agent_portrayal, 10, 10, 500, 500)
chart_element = ChartModule(["Safe", "Threat", "Responding"],
                            data_collector_name='datacollector')

server = ModularServer(ThreatDetectionModel,
                       [canvas_element, chart_element],
                       "AI Threat Detection Simulation",
                       {"width": 10, "height": 10, "N": 50})

server.port = 8521  # The default
server.launch()
